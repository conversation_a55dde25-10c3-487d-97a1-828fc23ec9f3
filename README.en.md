[Click me switch to Chinese version](README.md)

![](docs/images/Metis_logo.png)

[![license](http://img.shields.io/badge/license-BSD3-blue.svg)](https://github.com/tencent/Metis/master/LICENSE.TXT)
[![Release Version](https://img.shields.io/badge/release-0.2.0-red.svg)](https://github.com/tencent/Metis/releases)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/tencent/Metis/pulls)
 
The name **Metis** is taken from the Greek goddess of wisdom, <PERSON><PERSON>, which is a collection of application practices in the AIOps field. It mainly solves the problem of intelligent operation and maintenance in terms of quality, efficiency and cost. The current version of the open source time series anomaly detection learnware is to solve the anomaly detection problem of time series data from the perspective of machine learning.

The realization of the time series anomaly detection learnware is based on statistical judgment, unsupervised and supervised learning to jointly detect time series data. The first-level decision is made by statistical judgment and unsupervised algorithm, and the suspected abnormality is output. Secondly, the supervised model is judged, and the final test result is obtained. The detection model is generated through a large number of sample training and can be continuously updated according to the sample.

The time series anomaly detection learnware has been covered in **20w+** Zhiyun server, which carries the abnormality detection of **240w+** service indicators. After extensive monitoring and data polishing, the learnware has a wide range of applications in the field of anomaly detection and operation and maintenance monitoring.

## Support Platform

The operating system platform currently running is as follows:

- OS: Linux

## Support Language

The development languages supported by the front and back ends are as follows:

- Front-end: JavaScript, TypeScript
- Back-end: Python 2.7

## Overview

* [Use scenario](docs/usecase.md)
* [Code directory](docs/code_framework.md)
* [Code architecture](docs/architecture.md)

## Installation Guide

* When installing for the first time, please refer to the installation documentation: [install.md](docs/install.md)

## Instructions

* [WEB instructions](docs/web_userguide.md)
* [API instructions](docs/api_userguide.md)

## License

Metis is based on the BSD 3-Clause License, see for details: [LICENSE.TXT](LICENSE.TXT).

## Contributing

If you find a problem during use, please submit and describe via [https://github.com/Tencent/Metis/issues](https://github.com/Tencent/Metis/issues) , you can also view other issues here and contribute code by resolving these issues.

If you are contributing your code for the first time, please read [CONTRIBUTING](CONTRIBUTING.md) to learn about our contribution process.

Join our [Tencent OpenSource Plan](https://opensource.tencent.com/contribution).

## Contact Information

QQ technology exchange group: 288723616.

![qq_group](docs/images/qq_group.png)
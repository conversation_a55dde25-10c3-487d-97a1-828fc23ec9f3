{#
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
#}

import React from 'react';
import ReactDOM from 'react-dom';
import _ from 'lodash';
import PropTypes from 'prop-types';
import Model from '{{ libPath }}/class/Model';

{% for components in imports %}
import {{ components.name }} from '{{components.component}}';
{% endfor %}
{% for style in styles %}
import '{{style.src}}';
{% endfor %}

const _lang = {};
{% for lang in language %}
import {{ lang }} from '{{ appPath }}/language/{{ lang }}.json';
_lang['{{ lang }}'] = {{ lang }};
{% endfor %}

export default class {{ className }}Component extends React.Component {

  constructor(props, context) {
    super(props, context);
    const generator = new Model(this);
    this.$lang = _lang[props.lang];
    this.$model = {};
    this.$controller = {};
    this.$props = { ...props };
    this._isUnmounted = false;

    {% for model in models %}
    {
      let model = require('{{ model.src }}');
      model(generator.Page, generator.Model, generator.Controller, this);
    }
    {% endfor %}
  }

  _unload() {
    {% if isPage %}
    uw.event.emit('@@page_unloaded');
    {% endif %}
    this._isUnmounted = true;
  }

  render() {
    if (this.props.location) {
      this.props.location.query = uw.qs.parse(this.props.location.search.slice(1));
    }
    const $props = this.props;
    const $model = this.$model;
    const $controller = this.$controller;
    const $lang = this.$lang = _lang[$props.lang];
    mergeProps(this.$props, this.props);
    return (
      {{ uwx }}
    );
  }
}


{# 因为需要保证 this.$props 保持原对象的引用，因此只能以修改对象属性的方式来更新 this.$props #}
function mergeProps(props, newProps) {
  _.unset(props);
  _.assign(props, newProps);
}
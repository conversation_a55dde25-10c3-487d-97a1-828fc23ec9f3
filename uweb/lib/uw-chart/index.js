/*
  Tencent is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var i=e();for(var n in i)("object"==typeof exports?exports:t)[n]=i[n]}}(window,function(){return function(t){var e={};function i(n){if(e[n])return e[n].exports;var r=e[n]={i:n,l:!1,exports:{}};return t[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=t,i.c=e,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)i.d(n,r,function(e){return t[e]}.bind(null,r));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=8)}([function(t,e,i){"use strict";t.exports=i(3)},function(t,e){t.exports=function(t,e,i,n){var r=i?i.call(n,t,e):void 0;if(void 0!==r)return!!r;if(t===e)return!0;if("object"!=typeof t||!t||"object"!=typeof e||!e)return!1;var s=Object.keys(t),o=Object.keys(e);if(s.length!==o.length)return!1;for(var a=Object.prototype.hasOwnProperty.bind(e),h=0;h<s.length;h++){var l=s[h];if(!a(l))return!1;var u=t[l],c=e[l];if(!1===(r=i?i.call(n,u,c,l):void 0)||void 0===r&&u!==c)return!1}return!0}},function(t,e,i){window,t.exports=function(t){var e={};function i(n){if(e[n])return e[n].exports;var r=e[n]={i:n,l:!1,exports:{}};return t[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=t,i.c=e,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)i.d(n,r,function(e){return t[e]}.bind(null,r));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=0)}([function(t,e,i){"use strict";function n(t,e){return new a([[1,0,0],[0,1,0],[t,e,1]])}function r(t){var e=o(t.data,t.data.length);return function(t,e){for(var i=new a,n=t.data,r=0;r<n.length;r++)for(var s=0;s<n[r].length;s++)i.data[r][s]=n[r][s]*e;return i}(function(t,e){for(var i=new a,n=0;n<e;n++)for(var r=0;r<e;r++){var h=s(t.data,n,r),l=o(h,e-1);i.data[r][n]=(r+n)%2==0?l:-l}return i}(t,t.data.length),1/e)}function s(t,e,i){for(var n=[],r=0;r<t.length;r++)if(r!=e){for(var s=[],o=0;o<t[r].length;o++)o!=i&&s.push(t[r][o]);n.push(s)}return n}function o(t,e){var i;if(2==e)return t[0][0]*t[1][1]-t[0][1]*t[1][0];if(e>2){i=0;for(var n=0;n<e;n++){for(var r=[],s=1;s<e;s++){for(var a=[],h=0;h<e;h++)h!=n&&a.push(t[s][h]);r.push(a)}var l=t[0][n]*o(r,e-1);n%2==1&&(l=-l),i+=l}return i}}i.r(e);var a=function(){function t(t){if(void 0===t&&(t=void 0),this.data=[[1,0,0],[0,1,0],[0,0,1]],t)for(var e=0;e<3;e++)for(var i=0;i<3;i++)this.data[e][i]=t[e][i]}return t.prototype.clone=function(){for(var e=[],i=0;i<this.data.length;i++){for(var n=[],r=this.data[i],s=0;s<r.length;s++)n.push(r[s]);e.push(n)}return new t(e)},t}();function h(t,e){for(var i=new a,n=0;n<3;n++)for(var r=0;r<3;r++){i.data[n][r]=0;for(var s=0;s<3;s++)i.data[n][r]=i.data[n][r]+t.data[n][s]*e.data[s][r]}return i}function l(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var i=t;if(i&&i.length>1){for(var n=i[0],r=1;r<i.length;r++)n=h(n,i[r]);return n}}function u(t,e){return{x:t.x*e.data[0][0]+t.y*e.data[1][0]+e.data[2][0],y:t.x*e.data[0][1]+t.y*e.data[1][1]+e.data[2][1]}}function c(t,e,i){var r=e.height/t.height,s=e.width/t.width,o=n(e.x-t.x,e.y-t.y),h=function(t,e){return new a([[t=t||1,0,0],[0,e=e||1,0],[0,0,1]])}(s,r),u=new a;return i&&(u=new a([[1,0,0],[0,-1,0],[0,0,1]])),u=l(u,h,o)}function p(){let t=[];for(let e=0;e<36;e++)t[e]="**********abcdef".substr(Math.floor(16*Math.random()),1);return t[14]="4",t[19]="**********abcdef".substr(3&t[19]|8,1),t[8]=t[13]=t[18]=t[23]="-",t.join("")}function d(t,e,i){if(null==t)return t;if(null==e)return;let n=!0,r=e.split("."),s=t;for(let t=0;t<r.length;t++){let e=r[t];if(!s.hasOwnProperty(e)){n=!1;break}s=s[e]}return n?s:i}function f(t){let e=0,i=0,n=0,r=0;for(let s=0;s<t.length;s++)if(t[s]instanceof Array)for(let o=0;o<t[s].length;o++)n=Math.min(e,t[s][o]),isNaN(n)||(e=n),r=Math.max(i,t[s][o]),isNaN(r)||(i=r);return{min:e,max:i}}const g="".trim||function(){return this.replace(/^\s\s*/,"").replace(/\s\s*$/,"")};function y(t){return g.call(t)}function v(t){let e=typeof t;return"number"===e||"string"===e&&y(t).length>0&&!isNaN(t)&&!REMPTY_REG.test(t)}const x=/^(\d)+(.)?\d*%$/;function m(t,e){return Math.sqrt((e.x-t.x)*(e.x-t.x)+(e.y-t.y)*(e.y-t.y))}function _(t,e){return{x:t.x-e.x,y:t.y-e.y}}function b(t,e){return{x:e*t.x,y:e*t.y}}function w(t,e){return _(e,t)}function L(t,e,i,n,r,s){void 0===s&&(s=[]),A(t,[e,i],n,r,s)}function A(t,e,i,n,r){if(void 0===r&&(r=null),e)if(!t.setLineDash&&r&&r.length>0)for(var s=1;s<e.length;s++)P(t,e[s-1],e[s],i,n,r);else{for(t.save(),t.beginPath(),r&&r.length>0&&t.setLineDash(r),t.moveTo(e[0].x,e[0].y),s=1;s<e.length;s++)t.lineTo(e[s].x,e[s].y);i&&(t.strokeStyle=i),n&&(t.lineWidth=n),t.stroke(),t.closePath(),t.restore()}}function P(t,e,i,n,r,s){if(F(e)&&F(i)){var o=i.x-e.x,a=i.y-e.y;r=r||1;var h=Math.sqrt(o*o+a*a);if(!(h<=0)){var l=a/h,u=o/h,c=0;(s=s&&s.length>0?s:[1]).forEach(function(t){t>0&&(c+=t)}),c=c||1;for(var p=Math.floor(h/c)*s.length,d=e.x,f=e.y,g=0;g<p;g++){var y=(m=s[g%s.length])*u,v=m*l;g%2==0&&(t.save(),t.beginPath(),n&&(t.strokeStyle=n),t.lineWidth=r,t.moveTo(d,f),t.lineTo(d+y,f+v),t.closePath(),t.stroke(),t.restore()),d+=y,f+=v}for(var x=0;(i.x-d)*(i.x-e.x)+(i.y-f)*(i.y-e.y)>0;){y=(m=s[x%s.length])*u,v=m*l;var m,_=(i.x-d-y)*(i.x-e.x)+(i.y-f-v)*(i.y-e.y);x%2==0&&(_>0?(t.save(),t.beginPath(),t.lineWidth=r,n&&(t.fillStyle=n),t.moveTo(d,f),t.lineTo(d+y,f+v),t.closePath(),t.stroke(),t.restore()):_<0&&(t.save(),t.lineWidth=r,n&&(t.fillStyle=n),t.beginPath(),t.moveTo(d,f),t.lineTo(i.x,i.y),t.closePath(),t.stroke(),t.restore())),d+=y,f+=v,x++}}}}function C(t,e,i,n,r){if(void 0===r&&(r=null),e&&!(e.length<1)){t.save(),t.beginPath(),r&&t.setLineDash(r),t.moveTo(e[0].point.x,e[0].point.y);for(var s=1;s<e.length;s++)t.lineTo(e[s].point.x,e[s].point.y);i&&(t.strokeStyle=i),n&&(t.lineWidth=n),t.stroke(),t.closePath(),t.restore()}}function I(t,e,i,n,r,s){t.beginPath(),r&&(t.strokeStyle=r,t.fillStyle=s),t.arc(e,i,n,0,2*Math.PI),t.fill(),t.stroke(),t.closePath()}var S,R,M,E={};function k(t){if(E[t])return E[t];S&&document.body.contains(S)||(S=function(){var t=document.createElement("div");t.style.height="0px",t.style.width="0px",t.style.overflow="hidden",t.style.padding="0 0",t.style.margin="0 0";var e=document.createElement("div");return document.body.appendChild(t),t.appendChild(e),e.style.display="block",setTimeout(function(){document.body.removeChild(t)},6e5),e}()),S.innerText="Y",S.style.font=t;var e=window.getComputedStyle(S).height||"",i=parseInt(e);return E[t]=i,i}function B(t,e,i,n,r,s,o,a){void 0===s&&(s="12px sans-serif"),void 0===o&&(o="start"),void 0===a&&(a="top"),t.save();var h=t.font;t.font=s,t.fillStyle=r,t.textAlign=o,t.textBaseline=a,t.fillText(e,i,n),t.font=h,t.restore()}function V(t,e,i,n,r){if(e&&e.length>0&&i&&i.length>0){t.save(),t.beginPath(),t.moveTo(e[0].point.x,e[0].point.y);for(var s=1;s<e.length;s++){var o=e[s].point;if(o){var a=i[2*(s-1)+0],h=i[2*(s-1)+1];t.bezierCurveTo(a.x,a.y,h.x,h.y,o.x,o.y)}}r&&(t.lineWidth=r),t.strokeStyle=n,t.stroke(),t.closePath(),t.restore()}}function O(t,e,i){if(e){var n=t.font;t.font=i;var r=t.measureText(e).width;return t.font=n,r}return 0}function H(t,e,i){return!!(t&&e>=t.x&&i>=t.y&&e<=t.x+t.width&&i<=t.y+t.height)}function T(t,e,i){var n=0,r=0;if(e){var s=k(i);e.split("\n").forEach(function(e){var o=O(t,e,i);n=n<o?o:n,r+=s})}return{width:n,height:r}}function N(t,e,i,n,r,s){s=Math.min(s,n/2,r/2),t.beginPath(),t.moveTo(e+s,i),t.lineTo(e+n-s,i),t.arc(e+n-s,i+s,s,3*Math.PI/2,0),t.lineTo(e+n,i+r-s),t.arc(e+n-s,i+r-s,s,0,Math.PI/2),t.lineTo(e+s,i+r),t.arc(e+s,i+r-s,s,Math.PI/2,Math.PI),t.lineTo(e,i+s),t.arc(e+s,i+s,s,Math.PI,3*Math.PI/2),t.closePath()}function j(t,e){return void 0===e&&(e=!1),e?{x:t.x,y:t.y}:{x:Math.round(t.x),y:Math.round(t.y)}}function D(t,e,i){t.style.width=e+"px",t.style.height=i+"px";var n=window.devicePixelRatio||1;t.width=e*n,t.height=i*n,t.getContext("2d").scale(n,n)}function F(t){return t&&null!=t.x&&null!=t.y&&!isNaN(t.x)&&!isNaN(t.y)}var z={getOrigin:function(){return R},setOrigin:function(t){R=t},getYAxisEnd:function(){return M},setYAxisEnd:function(t){M=t}},Y=function(){function t(t,e){this._text=t,this._font=e,this._visible=!0}return t.prototype.setVisible=function(t){this._visible=t},t.prototype.getSize=function(t){return this._text||(this._size={width:0,height:0}),this._size||(this._size=T(t,this._text,this._font)),this._size},t.prototype.setPosition=function(t,e,i,n){if(void 0===i&&(i=void 0),void 0===n&&(n=void 0),this._x=t,this._y=e,this._size&&!isNaN(this._size.width)&&!isNaN(this._size.height)){var r=this._size.width,s=this._size.height;"left"==i?this._x=t:"middle"==i?this._x=t-r/2:"right"==i&&(this._x=t-r),"top"==n?this._y=e:"middle"===n?this._y=e-s/2:"bottom"==n&&(this._y=e-s)}},t.prototype.draw=function(t){this._visible&&this._text&&B(t,this._text,this._x,this._y,this._color,this._font)},t}(),W=function(){function t(t,e,i,s){void 0===s&&(s=null),this.xAxisLength=0,this.yAxisLength=0,this.yAxisLabels=[],this.xAxisLabels=[],this.yMarkingLines=[],this.origin={},this.xStickLines=[],this.yStickLines=[],this.font="12px sans-serif",this.yAxisPoints=[],this.yAxisLabeloffset=10,this.xAxisLabeloffset=10,this.xAxisPoints=[],this._needRefresh=!0,this.xIndexInterval=1,this._showXAxis=!0,this.axisParam={yAxis:{inside:!1,showAxisLine:!0,showAxisTick:!0,splitLine:{show:!0,lineStyle:{width:1,type:"dash",color:["#ccc"]}}},xAxis:{}},this._ignoreRounded=!1,this.setLength=function(t,e){this.xAxisLength=t,this.yAxisLength=e,this.xSpan=1==this.xStickCount?0:t/(this.xStickCount-1),this.ySpan=1==this.yStickCount?0:e/(this.yStickCount-1)},this.setXAxisLabels=function(t){this.xAxisLabels=t},this.setMatrix=function(t){this.matrix=t,this.inverseMatrix=r(t)},this.getXAxisPos=function(t){if(this.xAxisPoints)return this.xAxisPoints[t]},this.getYAxisPos=function(t){if(this.yAxisPoints)return this.yAxisPoints[t]},this.needRefresh=function(){return this._needRefresh},this.calculate=function(){this.xAxisPoints=[],this.yAxisPoints=[],this.xStickLines=[],this.yStickLines=[],this.yMarkingLines=[];var t=-1;this.inside&&(t=1);var e=-1;this.axisParam.yAxis.labelInside&&(e=1);var i=n(e*this.stickLength,0),r=n(this.xAxisLength,0),s=n(0,-t*this.stickLength);if(0===this.xSpan){var o=j(p=u({x:this.xAxisLength/2,y:0},this.matrix),!1);this.xAxisPoints.push(o),this.xStickLines.push([o,j(u(o,s),!1)])}else for(var a=Math.floor(this.xAxisLength/this.xSpan)+1,h=0;h<a;h++)o=j(p=u({x:h*this.xSpan,y:0},this.matrix),this._ignoreRounded),this.xAxisPoints.push(o),this.xStickLines.push([o,j(u(o,s),this._ignoreRounded)]);var l=this.ySpan||1,c=Math.floor(this.yAxisLength/l)+1;for(h=0;h<c;h++){var p;o=j(p=u({x:0,y:h*l},this.matrix),this._ignoreRounded),this.yAxisPoints.push(o),this.yStickLines.push([o,j(u(p,i),this._ignoreRounded)]),this.yMarkingLines.push([o,j(u(p,r),this._ignoreRounded)])}this.origin=j(u({x:0,y:0},this.matrix),this._ignoreRounded),this.yAxisEnd=j(u({x:0,y:this.yAxisLength},this.matrix),this._ignoreRounded),this.xAxisEnd=j(u({x:this.xAxisLength,y:0},this.matrix),this._ignoreRounded),this._needRefresh=!0,z.setOrigin(this.origin),z.setYAxisEnd(this.yAxisEnd)};var o=c({x:0,y:0,width:1,height:1},{x:t.x,y:t.y,width:1,height:1},!0);this.matrix=o,this.inverseMatrix=r(o),this.xAxisLength=t.width,this.yAxisLength=t.height,this.stickLength=4,this.xStickCount=e||5,this.yStickCount=i||5,this.xSpan=1==e?0:this.xAxisLength/(e-1),this.ySpan=1==i?0:this.yAxisLength/(i-1),this.yAxisLabeloffset=this.stickLength,this.xAxisLabeloffset=this.stickLength,this._needRefresh=!1,this.eventHandler=s}return t.prototype.setRounded=function(t){this._ignoreRounded=!0===t},t.prototype.setXIndexInterval=function(t){this.xIndexInterval=t},t.prototype.setStickLength=function(t){this.stickLength=t},t.prototype.setStickCounts=function(t,e){this.xStickCount=t,this.yStickCount=e,this.xSpan=1==t?0:this.xAxisLength/(t-1),this.ySpan=1==e?0:this.yAxisLength/(e-1)},t.prototype.setYAxisLabels=function(t){this.yAxisLabels=t},t.prototype.getRect=function(){return{x:this.origin.x,y:this.origin.y-this.yAxisLength,width:this.xAxisLength,height:this.yAxisLength}},t.prototype.setYAxisLabelInside=function(t){this.axisParam.yAxis.inside!=t&&(this.axisParam.yAxis.inside=t)},t.prototype.setYAxisLineVisible=function(t){this.axisParam.yAxis.showAxisLine!=t&&(this.axisParam.yAxis.showAxisLine=t)},t.prototype.setYAxisTickVisible=function(t){this.axisParam.yAxis.showAxisTick!=t&&(this.axisParam.yAxis.showAxisTick=t)},t.prototype.draw=function(t){var e=k(this.font);if(this.axisParam.yAxis.splitLine.show){var i=this.axisParam.yAxis.splitLine.lineStyle,n=i.type,r=i.color||["#ccc"];r=r instanceof Array?r:[r];for(var s=i.width,o=0;o<this.yMarkingLines.length;o++){var a=r[o%r.length];"dash"===n?P(t,this.yMarkingLines[o][0],this.yMarkingLines[o][1],a,s,[1,2]):A(t,this.yMarkingLines[o],a,s)}}if(this._showXAxis&&this.axisParam.yAxis.showAxisLine&&L(t,this.origin,this.yAxisEnd,null,null),this.axisParam.yAxis.showAxisTick)for(o=0;o<this.yStickLines.length;o++)A(t,this.yStickLines[o],null,null);if(this.yAxisLabels)for(o=0;o<this.yAxisPoints.length&&o<this.yAxisLabels.length;o++){var h=this.yAxisPoints[o];if(u=this.yAxisLabels[o]){var l=new Y(u,this.font);l.getSize(t),this.axisParam.yAxis.inside?l.setPosition(h.x+this.yAxisLabeloffset,h.y,"left","top"):l.setPosition(h.x-this.yAxisLabeloffset,h.y,"right","middle"),0==o&&l.setVisible(!1),l.draw(t)}}for(L(t,this.origin,this.xAxisEnd,null,null),o=0;o<this.xStickLines.length;o+=this.xIndexInterval)A(t,this.xStickLines[o],null,null);if(this.xAxisLabels)for(o=0;o<this.xAxisPoints.length&&o<this.xAxisLabels.length;o+=this.xIndexInterval){var u;if(h=this.xAxisPoints[o],u=this.xAxisLabels[o]){var c=O(t,u,this.font);B(t,u,h.x-c/2,h.y+this.xAxisLabeloffset+e/2,"#000",this.font,void 0,"middle")}}this._needRefresh=!1},t}(),X=["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],$=["#006EFF","#36D992","#FFC933","#FF584C","#9741D9","#1FC0CC","#7DD936","#FF9C19","#E63984","#655CE6","#47CC50","#BF30B6"],q=function(){function t(t){void 0===t&&(t=void 0),this.type=null,this.startIndex=-1,this.endIndex=-1,this.xStickCountMax=6,this.colorArray=[],this.setOption=function(t){var e=this;this.option=t;var i=this.getValuesArray();this.valueRange=f(i),this.option&&this.option.xAxis&&this.option.xAxis.data&&this.option.xAxis.data.length>0&&(this.startIndex=0,this.endIndex=this.option.xAxis.data.length-1),t.color instanceof Array&&t.color.forEach(function(t){t&&0===t.indexOf("#")&&t.length<8&&e.colorArray.push(t)}),X.forEach(function(t){e.colorArray.indexOf(t)<0&&e.colorArray.push(t)})},this.getXAxis=function(){return d(this.option,"xAxis.data")},this.createYAxisLabels=function(){var t=[],e=this.getValuesArray(),i=!1;if(e.forEach(function(t){t&&t.length>0&&(i=!0)}),!i)return[];for(var n=this.calculateAxisScale(e),r=this._getYAxisLabelFormatter(),s=0;s<n.count+1;s++){var o=Z(n.minSupport,G(s,n.scale));r?"number"==typeof(o=r(o))?t.push(o+""):"string"==typeof o&&t.push(o):t.push(o+"")}return t},this.createXAxisLabels=function(){var t=[];if(null==d(this.option,"xAxis.data"))return t;for(var e=this._getXAxisLabelFormatter(),i=this.startIndex;i<this.option.xAxis.data.length&&i<this.endIndex+1;i++){var n=this.option.xAxis.data[i];e?"number"==typeof(n=e(n))?t.push(n+""):"string"==typeof n&&t.push(n):t.push(n)}return t},this.getTitle=function(){return this.this.option.title},this.getGrid=function(){return this.option.grid?{paddingTop:this.option.grid.top,paddingBottom:this.option.grid.bottom,paddingLeft:this.option.grid.left,paddingRight:this.option.grid.right}:null},this.getYStickCount=function(){var t=this.getValuesArray();return this.calculateAxisScale(t).count+1},this.getXStickCount=function(){var t=this.getIndexRange();return t.endIndex-t.startIndex+1},this.calculateAxisScale=function(t){var e=4,i=1,n={maxSupport:0,minSupport:0,scale:1};if(t){var r=f(t),s=r.max,o=Math.min(r.min,0);0==s&&0==o||(n=function(t,e,i){var n=Math.abs(t),r=Math.abs(e),s=U(t),o=U(e),a=t,h=e,l=o;if(e==t&&0==e);else if(t<0&&e>0){var u=(o/s>10?r+o:o/s<.1?n+s:n+r)/5,c=U(u);l=Math.floor(u/c+.5)*c,h=Math.ceil(r/l)*l,a=t<0?-Math.ceil(n/l)*l:Math.ceil(n/l)*l}else if(t>=0){var u=r/5,c=U(u);l=Math.floor(u/c+.5)*c,h=Math.ceil(r/l)*l,a=0}else if(e<0){var u=n/5,c=U(u);l=Math.floor(u/c+.5)*c,h=0,a=t<0?-Math.ceil(n/l)*l:Math.ceil(n/l)*l}return{maxSupport:h,minSupport:a,scale:l}}(o,s),i=n.scale,e=(n.maxSupport-n.minSupport)/i)}return{scale:i,count:e,minSupport:n.minSupport,maxSupport:n.maxSupport}},this.option=t||{xAxis:{data:[]},yAxis:{data:[]},series:[]}}return t.prototype.setIndexRange=function(t,e){this.startIndex=t,this.endIndex=e},t.prototype.getIndexRange=function(){return{startIndex:this.startIndex,endIndex:this.endIndex}},t.prototype.getYAxisValueMax=function(){return this.valueRange.max},t.prototype.getXAxisCount=function(){return this.option.xAxis.data.length},t.prototype.getSeriesDataByIndex=function(t){return this.option.series&&this.option.series[t]&&this.option.series[t].data?this.option.series[t].data:[]},t.prototype.getSeriesNameByIndex=function(t){if(this.option.series&&this.option.series[t])return this.option.series[t]},t.prototype.getColorByIndex=function(t){var e=t%this.colorArray.length;return this.colorArray[e]},t.prototype.getPieColorByIndex=function(t){return $[t%$.length]},t.prototype.getValuesArray=function(){var t=[];if(this.option&&this.option.series)for(var e=0;e<this.option.series.length;e++)this.option.series[e]&&this.option.series[e].data&&t.push(this.option.series[e].data);return t},t.prototype.getSeries=function(){return this.option.series||[]},t.prototype.getIndexInterval=function(){var t=this.getIndexRange();if(!t)return 1;var e=t.endIndex-t.startIndex;return e<1?1:e<=this.xStickCountMax?1:Math.ceil(e/this.xStickCountMax)},t.prototype.createMatrix=function(t){var e=this.calculateAxisScale(this.getValuesArray()),i=this.getIndexRange(),r=c({x:0,y:0,width:i.endIndex-i.startIndex||1,height:e.count*e.scale},{x:t.x,y:t.y,width:t.width,height:t.height},!0),s=(e.maxSupport-e.minSupport)/e.scale;return r=l(r,n(0,e.minSupport/e.scale*(t.height/s)))},t.prototype.getDataZoom=function(){var t=this.option.dataZoom,e=0,i=100;if(t&&t[0]){var n=t[0];return"number"==typeof n.start&&(e=n.start),"number"==typeof n.end&&(i=n.end),e=(e=Math.min(e,100))<0?0:e,i=Math.max(e,i),{start:e/100,end:(i=i>100?100:i)/100}}return null},t.prototype._getYAxisLabelFormatter=function(){if(this.option.yAxis&&this.option.yAxis.axisLabel&&"function"==typeof this.option.yAxis.axisLabel.formatter)return this.option.yAxis.axisLabel.formatter},t.prototype._getXAxisLabelFormatter=function(){if(this.option.xAxis&&this.option.xAxis.axisLabel&&"function"==typeof this.option.xAxis.axisLabel.formatter)return this.option.xAxis.axisLabel.formatter},t.prototype._getTooltip=function(){if(this.option.tooltip)return this.option.tooltip},t.prototype.getPipelineType=function(){return d(this.option,"series.type")},t.prototype.createCoordinate=function(t){var e=this.getXStickCount(),i=this.getYStickCount(),n=new W(t,e,i);n.setStickLength(4);var r=this.createYAxisLabels();n.setYAxisLabels(r);var s=this.createXAxisLabels();return n.setXAxisLabels(s),n.calculate(),n},t.prototype.getFromOption=function(t){return d(this.option,t)},t}();function U(t){var e=1;if((t=Math.abs(t))>5)for(;t>5;)t/=10,e*=10;else if(t<.5&&0!=t)for(;t<.5;)t*=10,e/=10;return e}function Z(t,e){var i,n,r;try{i=t.toString().split(".")[1].length}catch(t){i=0}try{n=e.toString().split(".")[1].length}catch(t){n=0}return(t*(r=Math.pow(10,Math.max(i,n)))+e*r)/r}function G(t,e){var i=0,n=t.toString(),r=e.toString();try{i+=n.split(".")[1].length}catch(t){}try{i+=r.split(".")[1].length}catch(t){}return Number(n.replace(".",""))*Number(r.replace(".",""))/Math.pow(10,i)}let J={BIG_FONT:'60px "microsoft yahei",sans-serif',FONT:'14px/24px "Microsoft YaHei"',SMALL_FONT:'12px/24px "Microsoft YaHei"'};function K(t,e){t.style.cursor=e}var Q=function(){function t(t,e,i,n,r,s,o,a,h,l,u,c){this.width=0,this.height=0,this.btnWidth=0,this.btnHeight=0,this.progressHeight=0,this.progressPadding=0,this.leftBtnValue=0,this.rightBtnValue=1,this.paddingRight=0,this._needRefresh=!0,this.option=null,this.width=r||0,this.topCanvas=t,this.height=s||0,this.btnWidth=u||10,this.btnHeight=c||s,this.progressHeight=o,this.progressPadding=.1*this.height,this.leftBtnValue=a||0,this.rightBtnValue=h||1,this.paddingRight=0,this.x=i||0,this.y=n||0,this.lastPost=null,this.selectedBtn,this.valueChangecallback=function(t){l(t)},this.mousedownListener=this.mousedownListener.bind(this),this.mousemoveListener=this.mousemoveListener.bind(this),this.mouseupListener=this.mouseupListener.bind(this),this.mouseleaveListener=this.mouseleaveListener.bind(this),this.option=null,this.optionManager=e,this._needRefresh=!0,this._inVisible=!1}return t.prototype.resize=function(t,e,i,n){this.width=i,this.height=n,this.x=t,this.y=e,this._needRefresh=!0},t.prototype.setBoundRect=function(t){this.boundRect=t},t.prototype.beforeDraw=function(t){this.boundRect&&t&&(t.save(),t.clearRect(this.boundRect.x,this.boundRect.y,this.boundRect.width,this.boundRect.height),t.restore())},t.prototype.isInLeftBtn=function(t,e){return H({x:this.x+this.leftBtnValue*this.getProgressLength(),y:this.y+(this.height-this.btnHeight)/2,width:this.btnWidth,height:this.btnHeight},t,e)},t.prototype.isInRightBtn=function(t,e){return H({x:this.x+this.rightBtnValue*this.getProgressLength()+this.btnWidth,y:this.y+(this.height-this.btnHeight)/2,width:this.btnWidth,height:this.btnHeight},t,e)},t.prototype.isInProgress=function(t,e){return H({x:this.x+this.btnWidth+this.getProgressLength()*this.leftBtnValue,y:this.y+this.progressPadding,width:(this.rightBtnValue-this.leftBtnValue)*this.getProgressLength(),height:this.height-2*this.progressPadding},t,e)},t.prototype.getProgressLength=function(){return this.width-2*this.btnWidth-this.paddingRight},t.prototype.setCursorStyle=function(t,e,i){H(this.getRectExtend(),e,i)&&(this.isInLeftBtn(e,i)||this.isInProgress(e,i)||this.isInRightBtn(e,i)?K(t,"ew-resize"):K(t,"default"))},t.prototype.mousemoveListener=function(t){if(!this._inVisible){var e=(t=window.event||t).offsetX||t.layerX,i=t.offsetY||t.layerY;if(this.setCursorStyle(this.topCanvas,e,i),this.lastPost&&this.selectedBtn){var n=e-this.lastPost.x;if(0!==n){var r=n/this.getProgressLength();"left"===this.selectedBtn?(this.setLeftBtnValue(this.leftBtnValue+r),this._needRefresh=!0):"right"===this.selectedBtn?(this.setRightBtnValue(this.rightBtnValue+r),this._needRefresh=!0):"progress"===this.selectedBtn&&(r=r<0?this.leftBtnValue+r>=0?r:-this.leftBtnValue:this.rightBtnValue+r>=1?1-this.rightBtnValue:r,this.setLeftBtnValue(this.leftBtnValue+r),this.setRightBtnValue(this.rightBtnValue+r),this._needRefresh=!0),this.validate()&&(this.lastPost={x:e,y:i}),this.valueChangecallback()}}}},t.prototype.needRefresh=function(){return this._needRefresh},t.prototype.mouseleaveListener=function(){this.lastPost=null,this.selectedBtn=null},t.prototype.mousedownListener=function(t){var e=(t=window.event||t).offsetX||t.layerX,i=t.offsetY||t.layerY;this.lastPost={x:e,y:i},this.isInLeftBtn(e,i)?this.selectedBtn="left":this.isInRightBtn(e,i)?this.selectedBtn="right":this.isInProgress(e,i)?this.selectedBtn="progress":(this.selectedBtn=null,this.lastPost=null)},t.prototype.mouseupListener=function(){this.lastPost=null,this.selectedBtn=null},t.prototype.getBtnWidth=function(){return this.btnWidth},t.prototype.getBtnHeight=function(){return this.btnHeight},t.prototype.validate=function(){return this.leftBtnValue<=1&&this.leftBtnValue>=0&&this.rightBtnValue>=this.leftBtnValue&&this.rightBtnValue<=1},t.prototype.normalizeValue=function(){this.leftBtnValue<0?this.leftBtnValue=0:this.leftBtnValue>1?this.leftBtnValue=1:"number"!=typeof this.leftBtnValue&&(this.leftBtnValue=0),this.rightBtnValue<this.leftBtnValue?this.rightBtnValue=this.leftBtnValue:this.rightBtnValue>1?this.rightBtnValue=1:"number"!=typeof this.rightBtnValue&&(this.rightBtnValue=this.leftBtnValue)},t.prototype.setLeftBtnValue=function(t){t<0?t=0:t>this.rightBtnValue?t=this.rightBtnValue:t>1&&(t=1),this.leftBtnValue=t,this._needRefresh=!0},t.prototype.getLeftBtnValue=function(){return this.leftBtnValue},t.prototype.setRightBtnValue=function(t){t<0?t=0:t<this.leftBtnValue?t=this.leftBtnValue:t>1&&(t=1),this.rightBtnValue=t,this._needRefresh=!0},t.prototype.getRightBtnValue=function(){return this.rightBtnValue},t.prototype.getRectExtend=function(){return{x:this.x-10,y:this.y-10,width:this.width+20,height:this.height+20}},t.prototype.getRegion=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.repaint=function(){this._needRefresh=!0},t.prototype.setVisible=function(t){this._inVisible=!t,t&&(this._needRefresh=!0)},t.prototype.isVisible=function(){return!this._inVisible},t.prototype.draw=function(t){if(this._inVisible)this._needRefresh=!1;else{this.normalizeValue();var e=this.getProgressLength();this.drawBackground(t);var i=this.x+e*this.leftBtnValue,n=this.y+(this.height-this.btnHeight)/2;t.save(),t.beginPath(),N(t,i,n,this.btnWidth,this.btnHeight,5),t.closePath(),t.fillStyle="#fff",t.fill(),t.strokeStyle="#ccc",t.stroke(),t.restore();var r=this.x+this.btnWidth+e*this.rightBtnValue,s=this.y+(this.height-this.btnHeight)/2;t.save(),t.beginPath(),N(t,r,s,this.btnWidth,this.btnHeight,5),t.closePath(),t.fillStyle="#fff",t.fill(),t.strokeStyle="#ccc",t.stroke(),t.restore(),this._needRefresh=!1}},t.prototype.drawBackground=function(t){var e=this.optionManager.getSeriesDataByIndex(0),i=e.length-1,n=f([e]);i=i<0?0:i;var r=c({x:0,y:0,width:e.length-1,height:n.max-n.min},{x:this.x+this.btnWidth,y:this.y+this.height-this.progressPadding,width:this.getProgressLength(),height:this.height-2*this.progressPadding},!0),s=u({x:0,y:0},r),o=u({x:e.length-1,y:0},r),a=[];if(e&&e.length>0){for(var h=0;h<e.length;h++){var l=u({x:h,y:e[h]},r);a.push(l)}for(t.save(),t.beginPath(),t.moveTo(s.x,s.y),h=0;h<a.length;h++)t.lineTo(a[h].x,a[h].y);for(t.lineTo(o.x,o.y),t.closePath(),t.fillStyle="#E7E7E7",t.globalAlpha=.8,t.fill(),t.restore(),t.save(),t.beginPath(),t.moveTo(a[0].x,a[0].y),h=0;h<a.length;h++)t.lineTo(a[h].x,a[h].y);t.strokeStyle="#D6D6D6",t.stroke(),t.closePath(),t.restore()}t.save(),t.beginPath(),t.rect(this.x+this.btnWidth,this.y+this.progressPadding,this.getProgressLength(),this.height-2*this.progressPadding),t.closePath(),t.strokeStyle="#D6DEE7",t.lineWidth=1,t.stroke(),t.restore(),t.save(),t.beginPath(),t.rect(this.x+this.btnWidth+this.getProgressLength()*this.leftBtnValue,this.y+this.progressPadding,this.getProgressLength()*(this.rightBtnValue-this.leftBtnValue),this.height-2*this.progressPadding),t.closePath(),t.fillStyle="#D6DEE7",t.globalAlpha=.5,t.fill(),t.restore()},t}(),tt=function(){function t(){this.listenersMap={},this.mousemoveListener=this.mousemoveListener.bind(this),this.mousedownListener=this.mousedownListener.bind(this),this.mouseleaveListener=this.mouseleaveListener.bind(this),this.mouseupListener=this.mouseupListener.bind(this),this.mousewheelListener=this.mousewheelListener.bind(this)}return t.prototype.registerListener=function(t,e,i){if(void 0===i&&(i=null),t&&e)if(this.listenersMap[t]){var n=this.listenersMap[t];if(i){for(var r=0;r<n.length;r++){var s=n[r];if(s.listener===e&&s.context===i)return}n.push({listener:e,context:i})}else n.push({listener:e})}else this.listenersMap[t]=[],this.listenersMap[t].push({context:i,listener:e})},t.prototype.dispatchEvent=function(t,e){(this.listenersMap[t]||[]).concat().forEach(function(t){if(t.context)t.listener.call(t.context,e);else{var i=window||t;t.listener.call(i,e)}})},t.prototype.dispatchPropertyChangeEvent=function(t){this.dispatchEvent("propertyChange",t)},t.prototype.mousemoveListener=function(t){t=window.event||t,this.dispatchEvent("mousemove",t)},t.prototype.mousedownListener=function(t){t=window.event||t,this.dispatchEvent("mousedown",t)},t.prototype.mouseleaveListener=function(t){t=window.event||t,this.dispatchEvent("mouseleave",t)},t.prototype.mouseupListener=function(t){t=window.event||t,this.dispatchEvent("mouseup",t)},t.prototype.mousewheelListener=function(t){t=window.event||t,this.dispatchEvent("mousewheel",t)},t.prototype.unRegisterListener=function(t,e,i){if(t)if(null==e)this.listenersMap[t]=[];else{for(var n=(this.listenersMap[t]||[]).concat(),r=n.length-1;r>=0;r--){var s=n[r];s.listener===e&&s.context===i&&n.splice(r,1)}this.listenersMap[t]=n}},t}(),et=function(){function t(t){this.tipContainer=t,this.font=J.BIG_FONT}return t.prototype.show=function(t,e,i,n,r,s){if(!(d(i,"item.length")<1)){var o,a=t+(n=n&&"number"==typeof n?n:0),h=e+n,l=s&&s._getTooltip()?s._getTooltip():{show:!0};if("function"==typeof l.formatter&&0!=l.show)o=l.formatter(i.items);else if(0!=l.show){o=null!=i.title?i.title+"":null;var u=this.handleData(i),c=u.pie||[],p=u.line||[];for(var f in delete u.pie,delete u.line,p.forEach(function(t){null!=t.value&&(o=o||"",o+='<br/><span style="display:inline-block;margin-right:10px;border-radius:10px;width:10px;height:10px;background-color:'+t.color+';"></span>'+t.seriesName+":"+t.value)}),u)u[f].forEach(function(t){null!=t.value&&(o=o||"",o+='<br/><span style="display:inline-block;margin-right:10px;border-radius:10px;width:10px;height:10px;background-color:'+t.color+';"></span>'+t.seriesName+":"+t.value)});c.forEach(function(t){null!=t.value&&(o=o||"",o+=t.seriesName+"<br/><span>"+t.name+":"+t.value+"("+t.percent+"%)</span>")})}var g=this.tipContainer;if(o){g.innerHTML="";var y=function(t){var e=Object.assign({},{borderWidth:"0px",borderColor:"rgb(51, 51, 51)",borderRadius:"4px",display:"block",color:"black",font:'14px/24px "Microsoft YaHei"',padding:"5px",backgroundColor:"rgb(255,255,255)",border:"solid 1px #ccc",boxShadow:"0px 4px 4px -2px rgba(208,208,208,0.7)"},t),i=document.createElement("div");return it(i,e),i}(l.styles);g.appendChild(y),y.innerHTML=o,"block"!=g.style.display&&(g.style.display="block"),r&&(t+g.clientWidth+n>r.width&&(a=t-g.clientWidth-n),e+g.clientHeight+n>r.height&&(h=e-g.clientHeight-n)),g.style.left=a+"px",g.style.top=h+"px"}else this.hide()}},t.prototype.hide=function(){"none"!=this.tipContainer.style.display&&(this.tipContainer.style.display="none")},t.prototype.handleData=function(t){var e={},i=[];return t&&t.items&&(t.items.forEach(function(t){if(t){var n=t.seriesType;n?(e[n]=e[n]||[],e[n].push(t)):i.push(t)}}),e.line=e.line||[],i.forEach(function(t){t&&e.line.push(t)})),e},t}();function it(t,e){for(var i in e)t.style[i]=e[i]}var nt=function(){function t(){this.visible=!0,this.getPointInfo=function(){},this.getHover=function(){},this._matrix=new a,this.id=p(),this._inverseMatrix=new a,this.properties={}}return t.prototype.getName=function(){return this.name},t.prototype.setName=function(t,e){void 0===e&&(e=!1),this.name!=t&&(this.name=t,e||this.eventHandler.dispatchPropertyChangeEvent({type:"name",source:this}))},t.prototype.getSeriesName=function(){return this.seriesName},t.prototype.setSeriesName=function(t){this.seriesName=t},t.prototype.getSeriesIndex=function(){return this.seriesIndex},t.prototype.setSeriesIndex=function(t){this.seriesIndex=t},t.prototype.setSeriesType=function(t){this.seriesType=t},t.prototype.getSeriesType=function(){return this.seriesType},t.prototype.setColor=function(t){this.color=t},t.prototype.getColor=function(){return this.color},t.prototype.setMatrix=function(t,e){if(void 0===e&&(e=!1),!function(t,e){if(t===e)return!0;if(null==t&&null==e)return!0;if(null!=t&&null!=e){if(t.data.length!=e.data.length)return!1;for(var i=t.data,n=e.data,r=0;r<i.length;r++){var s=i[r],o=n[r];if(s.length!=o.length)return!1;for(var a=0;a<s.length;a++)if(s[a]!==o[a])return!1}return!0}return!1}(this._matrix,t)){var i=t.clone();this._matrix=i,this._inverseMatrix=r(i),e||this.eventHandler.dispatchPropertyChangeEvent({type:"matrix",source:this})}},t.prototype.getMatrix=function(){return this._matrix()},t.prototype.getId=function(){return this.id},t.prototype.setId=function(t){return this.id=t},t.prototype.isVisible=function(){return this.visible},t.prototype.setVisible=function(t,e){void 0===e&&(e=!1),this.visible!=t&&(e||this.eventHandler.dispatchPropertyChangeEvent({type:"visible",source:this})),this.visible=t},t.prototype.getRect=function(){if(null!=this.x&&null!=this.y&&null!=this.width&&null!=this.height)return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.setEventHandler=function(t){this.eventHandler=t},t.prototype.setZIndex=function(t,e){void 0===e&&(e=!1),t=parseInt(t,10)||0,this.zIndex!=t&&(e||this.eventHandler.dispatchPropertyChangeEvent({type:"zIndex",source:this}),this.zIndex=t)},t.prototype.getZIndex=function(){return this.zIndex},t.prototype.buildAnimation=function(){},t.prototype.destroy=function(){},t.prototype.getProperty=function(t){return this.properties[t]},t.prototype.setProperty=function(t,e){null!=t&&(this.properties[t]=e)},t}(),rt=function(){function t(t){this.point=t}return t.prototype.getPoint=function(){return this.point},t.prototype.setPoint=function(t){this.point=t},t.prototype.getXValue=function(){return this.xValue},t.prototype.setXValue=function(t){this.xValue=t},t.prototype.getYValue=function(){return this.yValue},t.prototype.setYValue=function(t){this.yValue=t},t.prototype.getIndex=function(){return this.index},t.prototype.setIndex=function(t){this.index=t},t}(),st=function(){function t(t,e,i){void 0===i&&(i={});var n=this;this._milisecond=t,this.callback=e,this._userData=i,this._timerObject={},this._loop=function(){if(n._hasCancle||n._hasDone)n._timerObject.timer&&(cancelAnimationFrame(n._timerObject.timer),n._timerObject.timer=null);else{var t=(new Date).getTime()-n._beginTime.getTime();t>=n._milisecond?(n.callback(n._milisecond,n._userData),n._hasDone=!0):(n.callback(t,i),window.cancelAnimationFrame(n._timerObject.timer),n._timerObject.timer=window.requestAnimationFrame(n._loop))}}}return t.prototype.run=function(){var t=this;this._hasRun=!0,this._delay?this._timerObject.delayTimer=setTimeout(function(){t._beginTime=new Date,t._loop()},this._delay):(this._beginTime=new Date,this._loop())},t.prototype.cancle=function(){this.hasDone()||(clearTimeout(this._timerObject.delayTimer),window.cancelAnimationFrame(this._timerObject.timer),this._hasCancle=!0)},t.prototype.hasCancle=function(){return this._hasCancle},t.prototype.hasFinish=function(){return this._hasCancle||this._hasDone},t.prototype.hasRun=function(){return this._hasRun},t.prototype.hasDone=function(){return this._hasDone},t.prototype.done=function(t){this.hasCancle()&&!t||this.hasDone()||(this.cancle(),this._hasDone=!0,this.callback(this._milisecond,this._userData))},t}(),ot=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),at=function(t){function e(e,i,n){var r=t.call(this)||this;return r.smooth=!1,r.controlPoints=[],r.indexInterval=1,r.startIndex=0,r.endIndex=0,r._drawPercent=1,r.getPointInfo=function(t,e){if(1===r.pointInfoList.length&&r.footPoint)return r.pointInfoList[0];var i=r._getXAxisIndexByPosition(t,e);return r.pointInfoList[i]},r.optionManager=e,r.valueArray=i.data,r.markLine=i.markLine,r._needCalculate=!0,r.eventHandler=n,r.zIndex=0,r.lineWidth=1,r}return ot(e,t),e.prototype.setColor=function(t,e){void 0===e&&(e=!1),this.color=t,e||this.eventHandler.dispatchPropertyChangeEvent({type:"color",source:this})},e.prototype.setLineWidth=function(t,e){void 0===e&&(e=!1),this.lineWidth=parseInt(t,10)||1,e||this.eventHandler.dispatchPropertyChangeEvent({type:"lineWidth",source:this})},e.prototype.getPointInfoList=function(){return this.pointInfoList},e.prototype.setIndexInterval=function(t,e){void 0===e&&(e=!1),this.indexInterval=t,e||this.eventHandler.dispatchPropertyChangeEvent({type:"interval",source:this})},e.prototype.getColor=function(){return this.color},e.prototype.setValues=function(t,e){void 0===e&&(e=!1),this.valueArray=t,this._needCalculate=!0,e||this.eventHandler.dispatchPropertyChangeEvent({type:"valueArray",source:this})},e.prototype.setIndexRange=function(t,e,i){void 0===i&&(i=!1),this.startIndex==t&&this.endIndex==e||(this._needCalculate=!0,i||this.eventHandler.dispatchPropertyChangeEvent({type:"IndexRange",source:this})),this.startIndex=t,this.endIndex=e},e.prototype.setSmooth=function(t,e){void 0===e&&(e=!1),this.smooth!=t&&(this.smooth=t,e||this.eventHandler.dispatchPropertyChangeEvent({type:"smooth",source:this}))},e.prototype.calculatePointInfo=function(){var t,e=[],i=[];if(this.valueArray){for(var n=this.endIndex-this.startIndex+1,r=0;r<n&&r<this.valueArray.length&&r+this.startIndex<this.valueArray.length;r++){var s,o,a;t=null,"string"==typeof(s=this.valueArray[r+this.startIndex])&&(0==(s=y(s)).length||isNaN(s))&&(s=null),null!=s&&(t=u({x:r,y:s},this._matrix)),i.push(t),(o=new rt(t)).setYValue(s),o.setIndex(r+this.startIndex),(a=this.optionManager.getXAxis())&&o.setXValue(a[r+this.startIndex]),e.push(o)}this.footPoint=null,1==n&&this.valueArray.length>this.startIndex&&(e=[],i=[],t=null,"string"==typeof(s=this.valueArray[0+this.startIndex])&&(0==(s=y(s)).length||isNaN(s))&&(s=null),null!=s&&(t=j(u({x:.5,y:s},this._matrix)),this.footPoint=j(u({x:.5,y:0},this._matrix))),i.push(t),(o=new rt(t)).setYValue(s),o.setIndex(0+this.startIndex),(a=this.optionManager.getXAxis())&&o.setXValue(a[0+this.startIndex]),e.push(o))}this.pointInfoList=e,this.controlPoints=function(t,e){e=e||.1;var i=[];if(t&&t.length>0){var n=t.concat();n.push(n[n.length-1]),n.unshift(n[0]);for(var r=n.length-1,s=1;s<r;s++){var o=null,a=null,h=n[s],l=n[s-1]||h,u=n[s+1]||h;if(null!=l&&null!=u&&null!=h){var c=w(l,u),p=m(h,l),d=m(h,u),f=e*d/(p+d);o=_(h,b(c,e*p/(p+d))),a=_(h,b(c,-f))}i.push(o),i.push(a)}}return i.shift(),i.pop(),i}(i,.3),this._needCalculate=!1},e.prototype.needCalculate=function(){return this._needCalculate},e.prototype._getXAxisIndexByPosition=function(t,e){var i=u({x:t,y:e},r(this._matrix)).x;return Math.round(i)},e.prototype.buildAnimation=function(){var t=this;return null!=this._animation&&this._animation.done(!0),null==this._animation&&(this._animation=new st(1e3,function(e){var i=e/1e3;i>1e3&&(i=1),t._drawPercent=i,t.eventHandler.dispatchPropertyChangeEvent({type:"animation",source:t})})),this._animation.run(),this._animation},e.prototype.draw=function(t){this.drawByPercent(t,this._drawPercent),this.drawMarkLine(t)},e.prototype.drawMarkLine=function(t){var e=this;this.markLine&&this.markLine.data&&this.markLine.lineStyle&&this.markLine.data.forEach(function(i){var n;if(e.pointInfoList.forEach(function(t){t.xValue===i.xAxis&&(n=t)}),n&&n.point){var r=n.point.x||0,s=z.getOrigin().y,o=z.getYAxisEnd().y;L(t,{x:r,y:s},{x:r,y:o},e.markLine.lineStyle.normal.color,1,[2,2]),I(t,r,s,3,e.markLine.lineStyle.normal.color,e.markLine.lineStyle.normal.color),I(t,r,o,3,e.markLine.lineStyle.normal.color,e.markLine.lineStyle.normal.color);var a=e.markLine.label.formatter(n.xValue),h=O(t,a,"12px sans-serif");B(t,a,r-h/2,o-10,e.markLine.lineStyle.normal.color,void 0,"start","middle")}})},e.prototype.drawByPercent=function(t,e){e=e<0?0:e;var i=this.pointInfoList;e&&e<1&&(i=this.pointInfoList.slice(0,Math.round(e*this.pointInfoList.length))),this.smooth?function(t,e,i,n,r){if(!(e.length<2)){var s=[],o=[],a=[],h=[],l=d(e[0],"point");l&&s.push(e[0]);for(var u=1;u<e.length;u++){var c=e[u];null==c.point?(s.length>=2&&(a.push(s),h.push(o)),o=[],s=[]):(l&&(o.push(i[2*(u-1)]),o.push(i[2*(u-1)+1])),s.push(c)),l=c.point}for(s.length>=2&&(a.push(s),h.push(o),o=[],s=[]),u=0;u<a.length;u++)V(t,a[u],h[u],n,r)}}(t,i,this.controlPoints,this.color,this.lineWidth):function(t,e,i,n,r){void 0===r&&(r=null);for(var s=[],o=[],a=0;a<e.length;a++){var h=e[a];d(h,"point")?s.push(h):(s.length>=2&&o.push(s),s=[])}for(s.length>=2&&(o.push(s),s=[]),a=0;a<o.length;a++)C(t,o[a],i,n,r)}(t,i,this.color,this.lineWidth),1===i.length&&this.footPoint&&A(t,[i[0].point,this.footPoint],this.color,this.lineWidth)},e.prototype.destroy=function(){this._animation&&(this._animation.cancle(),this._animation=null)},e}(nt),ht=function(){function t(){}return t.prototype.createChart=function(t,e,i,n){n=n||{};var r=t.getIndexRange();if(e&&e.data&&e.data.length>0){var s=new at(t,e,i);s.setIndexRange(r.startIndex,r.endIndex,!0),s.setSmooth(d(e,"smooth")),s.setName(e.name,!0),s.setZIndex(e.z,!0);var o=d(e,"lineStyle.normal.width");return s.setLineWidth(o),s.setColor(n.color),s}},t}(),lt=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ut=function(t){function e(e,i){var n=t.call(this)||this;return n.setName(e),n.setValue(i),n._invisible=!1,n._runingAngle=0,n._color="#000",n._extendsLength=0,n._radiusOuter=0,n._enableAnimation=!0,n._hover=!1,n}return lt(e,t),e.prototype.setName=function(t){this.name=t},e.prototype.getName=function(){return this.name},e.prototype.setParent=function(t){this._parent=t},e.prototype.getParent=function(){return this._parent},e.prototype.setSeriesIndex=function(t){this.seriesIndex=t},e.prototype.getSeriesIndex=function(){return this.seriesIndex},e.prototype.getSeriesName=function(){return this.seriesName},e.prototype.setSeriesName=function(t){this.seriesName=t},e.prototype.setValue=function(t){if(!v(t))throw"value is can't convert to number!";this.value=parseFloat(t)},e.prototype.getValue=function(){return this.value},e.prototype.setDataIndex=function(t){this.dataIndex=t},e.prototype.getDataIndex=function(){return this.dataIndex},e.prototype._isInSector=function(t,e){var i=Math.sqrt(t*t+e*e);if(0==i)return!0;var n=t/i,r=Math.acos(n);return e<0&&(r=2*Math.PI-r),r>=this.startAngle&&r<=this.startAngle+this.angle},e.prototype.setStartAngle=function(t){this.startAngle=t},e.prototype.setAngle=function(t,e){void 0===e&&(e=!0),this.angle!=t&&(this.angle=t,e&&this.beginAngleAnimation())},e.prototype.getAngle=function(){return this.angle},e.prototype.setVisible=function(t,e){this._invisible!=!t&&(this._invisible=!t,e||this._pieEventHandler.dispatchPropertyChangeEvent({type:"needCalculateAngle",source:this}),t||(this.setAngle(0,!1),this._runingAngle=0))},e.prototype.isVisible=function(){return!this._invisible},e.prototype.getAngleForDraw=function(){return this._runingAngle},e.prototype.setColor=function(t){this._color=t},e.prototype.getColor=function(){return this._color},e.prototype.setPieEventHandler=function(t){this._pieEventHandler=t},e.prototype.getRect=function(){if(this._parent instanceof gt)return this._parent.getRect()},e.prototype.drawLegend=function(t,e){var i=e.getColor(),n=e.getFont(),r=e.getRect(),s=e.getName(),o=T(t,s,n),a=r.width-o.width-10;a>0&&(t.save(),N(t,r.x+5,r.y+2,a,r.height-4,2),t.fillStyle=e.isDisable()?"#ccc":i,t.fill(),t.restore()),r.width>o.width&&B(t,s,r.x+r.width-o.width,r.y,e.isDisable()?"#ccc":"#000",n)},e.prototype.draw=function(t,e,i,n,r,s){s instanceof gt&&function(t,e,i,n,r,s,o,a){var h=0,l=0;isNaN(n)?1==n.length?h=l=n[0]:n.length>=2&&(h=n[0],l=n[1]):h=l=n,t.save(),t.strokeStyle=o,t.fillStyle=o,t.beginPath(),h==l||null==h?t.moveTo(e,i):t.arc(e,i,h,s,r,!0),t.arc(e,i,l,r,s,!1),t.closePath(),t.fill(),t.restore()}(t,e,i,[n,r+this._extendsLength],this.startAngle,this.startAngle+this.getAngleForDraw(),this._color)},e.prototype.beginAngleAnimation=function(){var t=this;if(this._angleAnimation&&(this._angleAnimation.cancle(),this._angleAnimation=null),this._enableAnimation){var e=this.angle-this._runingAngle,i=this._runingAngle;this._angleAnimation=new st(1e3,function(n){var r=n/1e3*e;t._runingAngle=i+r,n>=1e3&&(t._runingAngle=t.angle),t._pieEventHandler.dispatchPropertyChangeEvent({type:"animation",source:t})}),this._angleAnimation.hasRun()||this._angleAnimation.run()}},e.prototype.onEnter=function(){var t=this;this._radiusAnimation&&(this._radiusAnimation.cancle(),this._radiusAnimation=null),this._enableAnimation&&(this._radiusAnimation=new st(2e3,function(e){var i=e/2e3;t._extendsLength+=10*i,t._extendsLength>10&&(t._extendsLength=10),t._pieEventHandler&&t._pieEventHandler.dispatchPropertyChangeEvent({type:"animation",source:t})}),this._radiusAnimation.hasRun()||(this._hover=!0,this._radiusAnimation.run()))},e.prototype.onLeave=function(){var t=this;this._radiusAnimation&&(this._radiusAnimation.cancle(),this._radiusAnimation=null),this._enableAnimation&&(this._radiusAnimation=new st(2e3,function(e){var i=e/2e3;t._extendsLength-=10*i,t._extendsLength<0&&(t._extendsLength=0),t._pieEventHandler&&t._pieEventHandler.dispatchPropertyChangeEvent({type:"animation",source:t})}),this._radiusAnimation.hasRun()||this._radiusAnimation.run())},e.prototype.destroy=function(){this._radiusAnimation&&(this._radiusAnimation.cancle(),this._radiusAnimation=null),this._angleAnimation&&(this._angleAnimation.cancle(),this._angleAnimation=null)},e.prototype.buildTooltipInfo=function(){return{seriesType:"pie",seriesIndex:this.getSeriesIndex(),seriesName:this.getSeriesName(),name:this.getName(),dataIndex:this.getDataIndex(),value:this.getValue(),color:this.getColor(),percent:Math.floor(.5*this.getAngle()/Math.PI*1e4)/100}},e}(nt),ct=function(){function t(t,e,i){"number"==typeof t&&NaN!=t&&(t+=""),this.text=t,this.color=e,this.font=i}return t.prototype.draw=function(t){B(t,this.text,this.x,this.y,this.color,this.font)},t.prototype.setPostion=function(t,e){this.x=t,this.y=e},t.prototype.getSize=function(t){if(!this.size){var e=t.font.replace(/\d+px\s/g,this.font+" ");this.font=e,this.size=T(t,this.text,this.font)}return this.size},t}(),pt=function(){function t(t){var e=this;this.renderList=[],t&&t.forEach(function(t){if(t){var i=new ct(t.text,t.color,t.font);e.renderList.push(i)}})}return t.prototype.getSize=function(t){var e=0,i=0;return this.size||(this.renderList.forEach(function(n){var r=n.getSize(t);r&&(i<r.height&&(i=r.height),e+=r.width)}),this.size={width:e,height:i}),this.size},t.prototype.setPostion=function(t,e){this.x=t,this.y=e},t.prototype.draw=function(t){var e=this.x,i=this.y,n=this.getSize(t);this.renderList.forEach(function(r){var s=r.getSize(t);r.setPostion(e,i+(n.height-s.height)/2),e+=s.width}),this.renderList.forEach(function(e){e.draw(t)})},t}(),dt=function(){function t(t){var e=this;t=t||[],this.renderList=[];var i,n=[];t.forEach(function(t){if(i=i||[],null!=t&&null!=t.text){for(var e=t.text.split("\n"),r=0;r<e.length-1;r++){var s=e[r];i=i||[],s.length>0&&i.push({text:s,color:t.color,font:t.font}),i.length>0&&n.push(i),i=void 0}e[e.length-1].length>0&&(i=i||[]).push({text:e[e.length-1],color:t.color,font:t.font})}}),i&&i.length>0&&(n.push(i),i=null),n.forEach(function(t){var i=new pt(t);e.renderList.push(i)})}return t.prototype.draw=function(t,e,i){var n=0,r=0;this.renderList.forEach(function(e){var i=e.getSize(t);i&&(n=n<i.width?i.width:n,r+=i.height)});var s=e-n/2,o=i-r/2,a=0;this.renderList.forEach(function(e){var i=e.getSize(t);e.setPostion(s+(n-i.width)/2,o+a),a+=i.height}),this.renderList.forEach(function(e){e.draw(t)})},t}(),ft=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),gt=function(t){function e(e,i){var n=t.call(this)||this;return n._total=0,n._startAngle=0,n.getHover=function(t,e,i){var r;return n.handleHover(t,e),n._hoverList.forEach(function(t){var e=n.getChildById(t);e&&(r=e.buildTooltipInfo())&&(n.setLabelParam(r),i.items.push(r))}),n._hoverList.length>0},n.eventHandler=e,n._pipeEventHanlder=new tt,n._pipeEventHanlder.registerListener("propertyChange",n.onPiePropertyChangeListener,n),n._children=[],n._chart=i,n._hoverList=[],n}return ft(e,t),e.prototype.setWidth=function(t,e){void 0===e&&(e=!1);var i=parseFloat(t);i!=this.width&&(this._width=i,e||this.eventHandler.dispatchPropertyChangeEvent({type:"width",source:this}))},e.prototype.setHeight=function(t,e){void 0===e&&(e=!1);var i=parseFloat(t);i!=this.height&&(this._height=i,e||this.eventHandler.dispatchPropertyChangeEvent({type:"height",source:this}))},e.prototype.setChart=function(t){this._chart=t},e.prototype.setCenterX=function(t,e){if(void 0===e&&(e=!1),this._centerX!==t){this._centerX=this._validate(t);var i=this._convert(this._width,this._centerX),r=this._convert(this._height,this._centerY);e||this.eventHandler.dispatchPropertyChangeEvent({type:"x",source:this});var s=n(i,r);this.setMatrix(s)}},e.prototype.setCenterY=function(t,e){if(void 0===e&&(e=!1),this._centerY!==t){this._centerY=this._validate(t);var i=this._convert(this._width,this._centerY),r=this._convert(this._height,this._centerY);e||this.eventHandler.dispatchPropertyChangeEvent({type:"y",source:this});var s=n(i,r);this.setMatrix(s)}},e.prototype.setRadiusInner=function(t){this._radiusInner=this._validate(t)},e.prototype.setRadiusOuter=function(t){this._radiusOuter=this._validate(t)},e.prototype.setOptionManager=function(t){this.optionManager=t},e.prototype._getFloatFromPercentString=function(t){return parseFloat(t)},e.prototype._validate=function(t){return"number"===t||function(t){return"string"==typeof t&&x.test(t)}(t)?t:v(t)?parseFloat(t):void 0},e.prototype._convert=function(t,e){return"number"==typeof e?e:"string"==typeof e?t*parseFloat(e)/100:e},e.prototype.draw=function(t){for(var e=this._convert(this._width,this._centerX),i=this._convert(this._height,this._centerY),n=Math.min(this._width,this._height)/2,r=this._convert(n,this._radiusInner),s=this._convert(n,this._radiusOuter),o=this._startAngle,a=this.getChildren(),h=0;h<a.length;h++){var l=a[h];l&&l.isVisible()&&(l.setStartAngle(o),l.draw(t,e,i,r,s,this),o+=l.getAngleForDraw())}this._drawLabel(t,this.labelParam)},e.prototype.setLabelParam=function(t){this.labelParam=t},e.prototype._drawLabel=function(t,e){if(this._isVisible()){var i={x:0,y:0};if("center"===(this._labelPosition||"center")&&(i.x=this._convert(this._width,"50%"),i.y=this._convert(this._height,"50%")),e&&this._hoverList.length>0&&"function"==typeof this._hoverFormatter)(a=this._hoverFormatter(e))instanceof Array&&(this._labelRender=new dt(a),this._labelRender.draw(t,i.x,i.y));else if("function"!=typeof this._normalFormatter){if(e){var n=this._labelFontSize+"px ";if(!e||!e.name)return;var r=e.name,s=t.font.replace(/\d+px\s/g,n),o=T(t,r,s);B(t,r,i.x-o.width/2,i.y-o.height/2,e.color,s)}}else{var a,h=this.buildAllLabelParams();(a=this._normalFormatter(h))instanceof Array&&(this._labelRender=new dt(a),this._labelRender.draw(t,i.x,i.y))}}},e.prototype.setLabelPosition=function(t){this._labelPosition=t},e.prototype.setLabelFontSize=function(t){this._labelFontSize=t},e.prototype.getRect=function(){var t=this._convert(this._width,this._centerX),e=this._convert(this._height,this._centerY),i=Math.min(this._width,this._height)/2,n=this._convert(i,this._radiusOuter);return{x:t-n,y:e-n,width:2*n,height:2*n}},e.prototype.onPiePropertyChangeListener=function(t){(t.source instanceof ut||t.source instanceof e)&&(this.eventHandler.dispatchPropertyChangeEvent({type:"animation",source:this}),t.source instanceof ut&&"needCalculateAngle"===t.type&&this.calculateItemAngle())},e.prototype.getChildren=function(){return this._children},e.prototype.addChildren=function(t){var e=this;t.forEach(function(t){e._children.push(t),t.setParent(e),t.setPieEventHandler(e._pipeEventHanlder)}),this.calculateItemAngle()},e.prototype.removeByName=function(t){for(var e=this._children.length-1;e>=0;e--){var i=this._children[e];i.name===t&&this.removes([i])}},e.prototype.removes=function(t){if(t){for(;t.length>0;){var e=t.pop();if(e){var i=this._children.indexOf(e);i>=0&&this._children.splice(i,1),e.destroy()}}this.calculateItemAngle()}},e.prototype.calculateItemAngle=function(){var t=this;this._total=0;var e=0;this._children.forEach(function(e){e.isVisible()&&(t._total+=e.getValue())}),this._children.forEach(function(i){i.isVisible()&&(e=i.getValue()*Math.PI*2/t._total,i.setAngle(e,!0))})},e.prototype.destroy=function(){this._children.forEach(function(t){t&&t&&t.destroy()})},e.prototype.handleHover=function(t,e){var i=this,n=u({x:t,y:e},this._inverseMatrix),r=Math.sqrt(n.x*n.x+n.y*n.y),s=Math.min(this._width,this._height)/2,o=this._convert(s,this._radiusInner),a=this._convert(s,this._radiusOuter),h=this.getChildren(),l=[],c=this._hoverList.concat(),p=[];if(null==o&&r<a||r>o&&r<a)for(var d=this._startAngle,f=0;f<h.length;f++){var g=h[f];if(g.setStartAngle(d),d+=g.getAngle(),g._isInSector(n.x,n.y)){l.push(g.getId());break}}return l.forEach(function(t){var e=c.indexOf(t);e<0?p.push(t):c.splice(e,1)}),c.forEach(function(t){var e=i.getChildById(t);e&&e.onLeave()}),p.forEach(function(n){var r=i.getChildById(n);r&&r.onEnter(t,e)}),this._hoverList.length>0&&0==l.length?this.onLeave():0==this._hoverList.length&&l.length>0&&this.onEnter(),this._hoverList=l,!1},e.prototype.onLeave=function(){this.setLabelParam(null)},e.prototype.onEnter=function(){},e.prototype.getChildById=function(t){var e=null;return this._children.forEach(function(i){i.getId()!==t||(e=i)}),e},e.prototype.setNormalFormatter=function(t){this._normalFormatter=t},e.prototype.setHoverFormatter=function(t){this._hoverFormatter=t},e.prototype.buildAllLabelParams=function(){var t=[],e=0,i={};if(this._children)return this._children.forEach(function(i){var n=i.buildTooltipInfo();n.visible=i.isVisible(),e+=n.value,t.push(n)}),i.all=t,i.total=e,i.seriesName=this.getSeriesName(),i},e.prototype._isVisible=function(){if(null==this._children||this._children.length<0)return!1;var t=!1;return this._children.forEach(function(e){e&&e.isVisible()&&(t=!0)}),t},e}(nt),yt=function(){function t(){}return t.prototype.createChart=function(t,e,i,n){var r=new gt(i,n.chart),s=d(e,"center",["50%","50%"]),o=d(e,"radius",["0%","100%"]),a=[r];r.setWidth(n.width),r.setHeight(n.height),r.setCenterX(s[0]),r.setCenterY(s[1]),r.setOptionManager(t),o instanceof Array?o.length>1?(r.setRadiusInner(o[0]),r.setRadiusOuter(o[1])):r.setRadiusOuter(o[0]):r.setRadiusOuter(o);var h=[],l=0;(e.data||[]).forEach(function(i){if(v(i.value)){var r=new ut(i.name,i.value);r.setSeriesName(e.name),r.setSeriesIndex(n.seriesIndex),r.setDataIndex(l),r.setColor(t.getPieColorByIndex(l)),h.push(r),a.push(r)}l++}),r.addChildren(h);var u=function(t){var e=d(t,"label.emphasis.textStyle.fontSize","12"),i=d(t,"label.normal.position","center");return{fontSize:parseInt(e),position:i}}(e);r.setLabelPosition(u.position),r.setLabelFontSize(u.fontSize);var c=function(t){return d(t,"label.formatters")}(e);return c&&(r.setHoverFormatter(d(c,"hover")),r.setNormalFormatter(d(c,"normal"))),r.setSeriesType("pie"),r.setSeriesName(e.name),a},t}(),vt=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),xt=function(t){function e(e,i,n){var r=t.call(this)||this;return r.optionManager=e,r.valueArray=i.data,r.eventHandler=n,r}return vt(e,t),e.prototype.setColor=function(t,e){void 0===e&&(e=!1),this.color=t,e||this.eventHandler.dispatchPropertyChangeEvent({type:"color",source:this})},e.prototype.draw=function(t){console.log("draw bar:",t)},e.prototype.destroy=function(){},e}(nt),mt=function(){function t(){}return t.prototype.createChart=function(t,e,i,n){if(n=n||{},e&&e.data&&e.data.length>0)return new xt(t,e,i)},t}(),_t=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},bt=function(){function t(t){this.pipelines=[],this.visibleList=[],this.dirtyList=[],this.dirtyMap={},this.eventHandler=t}return t.prototype.initPipelines=function(t,e){this.setPipelines(function(t,e,i){var n=[],r=0;return t.getFromOption("series",[]).forEach(function(s){if(s){var o;switch(s.type){case"line":var a=t.getColorByIndex(r);o=(new ht).createChart(t,s,e,_t({},i,{color:a,seriesIndex:r}));break;case"pie":o=(new yt).createChart(t,s,e,_t({},i,{seriesIndex:r}));break;case"bar":o=(new mt).createChart(t,s,e,_t({},i,{seriesIndex:r}));break;default:o=null}o instanceof Array?o.forEach(function(t){t instanceof nt&&n.push(t)}):null!=o&&(o.setSeriesType(s.type),n.push(o)),r++}}),n}(t,this.eventHandler,e)),this.sortPipeline()},t.prototype.setPipelines=function(t){var e=this;this.dirtyList=[],this.dirtyMap={},this.pipelines=t||[],t.forEach(function(t){e.dirty(t),t.setEventHandler(e.eventHandler)}),this.sortPipeline()},t.prototype.getPipelines=function(){return this.pipelines||[]},t.prototype.getPipelineById=function(t){for(var e=this.getPipelines(),i=0;i<e.length;i++){var n=e[i];if(n&&n.getId()===t)return n}},t.prototype.getPipelineByName=function(t){for(var e=this.getPipelines(),i=0;i<e.length;i++){var n=e[i];if(n&&n.getName()===t)return n}},t.prototype.dirty=function(t){if(t instanceof nt){var e=t.getId();this.dirtyList.indexOf(e)<0&&this.dirtyList.push(e),this.dirtyMap[e]=t}},t.prototype.removeFromDirty=function(t){if(t){var e=t.getId(),i=this.dirtyList.indexOf(e);i>=0&&this.dirtyList.splice(i,1),delete this.dirtyMap[e]}},t.prototype.sortPipeline=function(){if(this.pipelines){var t=this.pipelines.concat();this.pipelines=t.sort(function(t,e){var i=t.getZIndex(),n=e.getZIndex();return(i=isNaN(i)?0:i)<(n=isNaN(n)?0:n)?-1:i>n?1:0})}},t.prototype.isDirty=function(){return this.dirtyList.length>0},t.prototype.clearDirty=function(){this.dirtyList=[],this.dirtyMap=[]},t}(),wt=function(){function t(){this.font="12px STSong SimSun"}return t.prototype.setId=function(t){this.id=t},t.prototype.getId=function(){return this.id},t.prototype.getName=function(){return this.name},t.prototype.setName=function(t){this.name=t},t.prototype.getColor=function(){return this.color},t.prototype.setColor=function(t){this.color=t},t.prototype.isDisable=function(){return this.disable},t.prototype.setDisable=function(t){this.disable=t},t.prototype.getPosition=function(){return{x:this.x,y:this.y}},t.prototype.setPosition=function(t,e){this.x=t,this.y=e},t.prototype.getRect=function(){var t=this.getSize();return t&&null!=t.width&&null!=t.height&&null!=this.x&&null!=this.y?{x:this.x,y:this.y,width:t.width,height:t.height}:{}},t}(),Lt=function(){function t(t,e,i,n){this.font="12px STSong SimSun",this.align="right",this._invisible=!1,this.alignLeft=function(){var t=0,e=0,i=0,n=this.canvas.getContext("2d");this.renderList=[];for(var r=0;r<this.items.length;r++){var s=this.items[r],o=s.getSize(n);o.width>this.width&&e<=0?(s.setPosition(this.x,this.y+t),t+=o.height,e=0,i=0):this.width<e+o.width?(t+=i,s.setPosition(this.x,this.y+t),i=o.height,e=o.width):(s.setPosition(this.x+e,this.y+t),e+=o.width,i=0==i||i<o.height?o.height:i),this.renderList.push(s)}this.height=t+i},this.alignRight=function(){var t=0,e=0,i=0,n=this.canvas.getContext("2d");this.renderList=[];for(var r=this.calculateOrder(),s=0;s<r.length;s++){var o=r[s],a=this.items[o],h=a.getSize(n);h.width>this.width&&e<=0?(a.setPosition(this.x,this.y+t),t+=h.height,e=0,i=0):this.width<e+h.width?(t+=i,a.setPosition(this.x+this.width-h.width,this.y+t),i=h.height,e=h.width):(e+=h.width,a.setPosition(this.x+this.width-e,this.y+t),i=0==i||i<h.height?h.height:i),this.renderList.push(a)}this.height=t+i},this.canvas=t,this.width=n,this.x=e,this.y=i,this.itemMap={},this.height=0,this.paddingBottom=20,this._needRefresh=!0,this.renderList=[],this.items=[]}return t.prototype.setEventHandler=function(t){this.eventHandler=t},t.prototype.setAlign=function(t){t!==this.align&&(this.align=t,this.calculate())},t.prototype.calculate=function(){"left"===this.align?this.alignLeft():this.alignRight()},t.prototype.calculateOrder=function(){for(var t=0,e=0,i=[],n=[],r=this.canvas.getContext("2d"),s=0;s<this.items.length;s++){var o=this.items[s].getSize(r);if(o.width>this.width&&t<=0)o.height,t=0,e=0,i.push(s);else if(this.width<t+o.width){for(e=o.height,t=o.width;n.length>0;)i.push(n.pop());n.push(s)}else t+=o.width,e=0==e||e<o.height?o.height:e,n.push(s)}for(;n.length>0;)i.push(n.pop());return i},t.prototype.getRect=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.getRectExtend=function(){return{x:this.x-10,y:this.y-10,width:this.width+20,height:this.height+20}},t.prototype.setFont=function(t){this.font=t},t.prototype.setWidth=function(t){this.width=t},t.prototype.addLegends=function(t){var e;if(t instanceof Array)for(var i=0;i<t.length;i++){var n=t[i];if(n instanceof wt){var r=n.getName();r&&!this.itemMap[r]&&(this.items.push(n),this.itemMap[r]=n,e=!0)}}e&&(this.calculate(),this._needRefresh=!0)},t.prototype.removeLegend=function(t){if(this.itemMap[t]){var e=this.itemMap[t],i=this.items.indexOf(e);i>=0&&this.items.splice(i,1),delete this.itemMap[t]}},t.prototype.updateColor=function(t,e){this.renderList.forEach(function(i){i.getName()!==t||i.setColor(e)})},t.prototype.draw=function(t){if(!this._invisible){t=t||this.canvas.getContext("2d"),this.clearRect();for(var e=0;e<this.renderList.length;e++)this.renderList[e].draw(t);this._needRefresh=!1}},t.prototype.clearRect=function(){if(this.canvas){var t=this.canvas.getContext("2d");t.save(),t.clearRect(this.x,this.y,this.width,this.height),t.restore()}},t.prototype.needRefresh=function(){return this._needRefresh},t.prototype.refresh=function(){this._needRefresh=!0},t.prototype.getHeight=function(){return this.height+this.paddingBottom},t.prototype.getLegendItemByPos=function(t,e){if(H(this.getRect(),t,e))for(var i=0;i<this.renderList.length;i++){var n=this.renderList[i],r=n.getRect();if(r&&H(r,t,e))return n}},t.prototype.isVisible=function(){return!this._invisible},t.prototype.setVisible=function(t,e){void 0===e&&(e=!1),this._invisible!=!t&&(this._invisible=!t,!e&&this.eventHandler&&this.eventHandler.dispatchPropertyChangeEvent({type:"visible",source:this}))},t}();!function(){let t=0,e=["webkit","moz"];for(let t=0;t<e.length&&!window.requestAnimationFrame;++t)window.requestAnimationFrame=window[e[t]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[e[t]+"CancelAnimationFrame"]||window[e[t]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(e,i){let n=(new Date).getTime(),r=Math.max(0,16.7-(n-t)),s=window.setTimeout(function(){e(n+r)},r);return t=n+r,s}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(t){clearTimeout(t)})}();var At=function(){function t(t){this.chart=t}return t.prototype.handle=function(t){var e=this;if(t.source instanceof nt){var i=t.source;if("zIndex"===t.type&&this.chart.dataContainer.sortPipeline(),"color"===t.type&&this.chart.legend){var n=i.getColor(),r=i.getName();this.chart.legend.updateColor(r,n),this.chart.legend.refresh()}this.chart.dataContainer.getPipelines().forEach(function(t){e.chart.dataContainer.dirty(t)})}else if("mousewheel"===t.type){var s=t.value,o=this.chart.getProperty("mousewheelSpeed")||.01,a=this.chart.progressBar.leftBtnValue,h=this.chart.progressBar.rightBtnValue;this.chart.progressBar.setLeftBtnValue(a+o*s),this.chart.progressBar.setRightBtnValue(h-o*s),this.chart._clearTopCanvas(),this.chart._handleXAsixChange()}else t.source instanceof Lt&&"visible"==t.type&&(t.source.refresh(),this.chart.calculateLayout(),this.chart.transformChartLine())},t}(),Pt=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ct=function(t){function e(e,i){var n=t.call(this)||this;return n.getSize=function(t){return n.size||(n.size=n.calculateSize(t)),n.size},n.draw=function(t){var e=n.getSize(t),i={x:n.x,y:n.y+e.height/2},r={x:n.x+40,y:n.y+e.height/2},s=n.isDisable()?"#ccc":n.color,o={x:(i.x+r.x)/2,y:n.y+e.height/2};L(t,i,r,s,2),t.save(),t.beginPath(),t.arc(o.x,o.y,n.pointRadius/2,0,2*Math.PI),t.closePath(),t.lineWidth=1,t.strokeStyle=n.isDisable()?"#ccc":n.color,t.fillStyle="#ffffff",t.fill(),t.stroke(),t.restore(),B(t,n.name,n.x+45,n.y,n.isDisable()?s:"#000",n.font)},n.name=e,n.color=i,n.pointRadius=12,n}return Pt(e,t),e.prototype.calculateSize=function(t){var e=O(t,this.name,this.font),i=k(this.font),n=e+60;return this.width=n,this.height=i,{width:n,height:i,labelWidth:e}},e}(wt),It=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),St=function(t){function e(e,i){var n=t.call(this)||this;return n.paddingLeft=10,n.colorWidth=10,n.colorHeight=8,n.colorPaddingLR=4,n.getSize=function(t){return n.size||(n.size=n.calculateSize(t)),n.size},n.draw=function(t){var e=n.getRect(),i=n.getName(),r=T(t,i,n.font);t.save(),t.beginPath(),t.rect(n.x+n.paddingLeft+n.colorPaddingLR,n.y+(e.height-n.colorHeight)/2,n.colorWidth,n.colorHeight),t.fillStyle=n.isDisable()?"#ccc":n.color,t.closePath(),t.fill(),t.restore(),e.width>r.width&&B(t,i,e.x+e.width-r.width,e.y,n.isDisable()?"#ccc":"#000",n.font)},n.name=e,n.color=i,n.pointRadius=12,n}return It(e,t),e.prototype.calculateSize=function(t){var e=O(t,this.name,this.font),i=k(this.font),n=e+this.paddingLeft+this.colorWidth+2*this.colorPaddingLR;return this.width=n,this.height=i,{width:n,height:i,labelWidth:e}},e}(wt),Rt={},Mt="uw-chart-instance",Et={paddingTop:20,paddingBottom:30,paddingLeft:10,paddingRight:10},kt=function(){function t(t,e){if(this.topCanvas=null,this.middleCanvas=null,this.bottomCanvas=null,this.tipContainer=null,this.barCanvas=null,this.parentDom=null,this._needRefresh=!0,this._paintAll=!0,this.width=0,this.height=0,this.optionManager=new q,this.paddings=Object.assign({},Et),this.barHeight=40,this.tootltipTimer=null,this._hoverList=[],this._initEventListener=function(){var t=this;this._dettachEventListener(),this.container.addEventListener("mousedown",this.eventHandler.mousedownListener),this.container.addEventListener("mouseup",this.eventHandler.mouseupListener),this.container.addEventListener("mouseleave",this.eventHandler.mouseleaveListener),this.container.addEventListener("mousemove",this.eventHandler.mousemoveListener),this.container.addEventListener("mousewheel",this.eventHandler.mousewheelListener),this.eventHandler.registerListener("mousedown",this.progressBar.mousedownListener),this.eventHandler.registerListener("mousemove",this.progressBar.mousemoveListener),this.eventHandler.registerListener("mouseup",this.progressBar.mouseupListener),this.eventHandler.registerListener("mouseleave",this.progressBar.mouseleaveListener),this.eventHandler.registerListener("mouseleave",function(){t._clearTopCanvas()}),this.eventHandler.registerListener("mouseleave",function(){t._leaveCanvas()}),this.eventHandler.registerListener("mousemove",this._coordinateRegionListener),this.eventHandler.registerListener("mousedown",function(){t._legendClick(event)}),this.eventHandler.registerListener("mousemove",function(){t._legendSelect(event)}),this.eventHandler.registerListener("propertyChange",function(e){t._onPropertyChange(e)}),this.eventHandler.registerListener("mousewheel",function(e){t._coordinateRegionWheelListener(e)})},this._dettachEventListener=function(){this.container.removeEventListener("mousedown",this.eventHandler.mousedownListener),this.container.removeEventListener("mouseup",this.eventHandler.mouseupListener),this.container.removeEventListener("mouseleave",this.eventHandler.mouseleaveListener),this.container.removeEventListener("mousemove",this.eventHandler.mousemoveListener),this.container.removeEventListener("mousewheel",this.eventHandler.mousewheelListener),this.eventHandler.unRegisterListener("mousedown"),this.eventHandler.unRegisterListener("mousemove"),this.eventHandler.unRegisterListener("mouseup"),this.eventHandler.unRegisterListener("mouseleave"),this.eventHandler.unRegisterListener("propertyChange"),this.eventHandler.unRegisterListener("mousewheel")},this.parentDom=e,this._instanceId=p(),e){var i=e.getAttribute(Mt);i&&Rt[i]&&(Rt[i].destroy(),delete Rt[i]),e.setAttribute(Mt,this._instanceId)}this.type=t;var n=function(t){if(t){var e=document.createElement("div");e.style.position="relative";var i=Bt(),n=Bt(),r=Bt(),s=Bt(),o=function(){var t=document.createElement("div");return it(t,{position:"absolute",display:"none",whiteSpace:"nowrap",zIndex:"9999999",transition:"left 0.4s cubic-bezier(0.23, 1, 0.32, 1), top 0.2s cubic-bezier(0.23, 1, 0.32, 1)",left:"10px",top:"10px"}),t}();return t.appendChild(e),e.appendChild(n),e.appendChild(s),e.appendChild(r),e.appendChild(i),e.appendChild(o),{container:e,topCanvas:i,barCanvas:n,middleCanvas:r,bottomCanvas:s,tipContainer:o}}}(e);Rt[this._instanceId]=this,this.container=n.container,this.topCanvas=n.topCanvas,this.middleCanvas=n.middleCanvas,this.bottomCanvas=n.bottomCanvas,this.tipContainer=n.tipContainer,this.barCanvas=n.barCanvas,this.progressBar=this.createProgressBar(this.barHeight),this._coordinateRegionListener=this._coordinateRegionListener.bind(this),this.eventHandler=new tt,this._initEventListener(),this.tooltip=new et(this.tipContainer),this.legend=new Lt(this.bottomCanvas,0,0,this.width),this.legend.setEventHandler(this.eventHandler),this.dataContainer=new bt(this.eventHandler),this.propertyChangeHandler=new At(this),this.properties={},this.survivalMonitor()}return t.prototype.setOption=function(t){var e=this;this.optionManager.setOption(t);var i=this.parentDom.clientWidth,n=this.parentDom.clientHeight,r={width:parseInt(i),height:parseInt(n),chart:this},s=0!=this.optionManager.getFromOption("legend.show");this.legend.setVisible(!!s,!0),this.dataContainer.initPipelines(this.optionManager,r),this.setSize(i,n,this.optionManager.getGrid()),this.setCoordinateStyle(),this.transformChartLine(),function(t){let e=function(){t&&t(),requestAnimationFrame(e,16)};requestAnimationFrame(e,16)}(function(){e.needRefresh()&&e.draw()})},t.prototype.setCoordinateStyle=function(){var t=this.optionManager.getFromOption("yAxis")||{},e=d(t,"axisLine.show",!0),i=d(t,"axisTick.show",!0),n=d(t,"axisLabel.inside",!1);this.coordinate.setYAxisLabelInside(n),this.coordinate.setYAxisLineVisible(e),this.coordinate.setYAxisTickVisible(i);var r=d(t,"splitLine",{});"object"==typeof r&&(this.coordinate.axisParam.yAxis.splitLine=Object.assign({},this.coordinate.axisParam.yAxis.splitLine,r))},t.prototype.transformChartLine=function(){var t=this;this._animationArray=[];var e={x:this.coordinate.origin.x,y:this.coordinate.origin.y,width:this.coordinate.xAxisLength,height:this.coordinate.yAxisLength},i=this.optionManager.createMatrix(e);this.dataContainer.getPipelines().forEach(function(e){if(e instanceof nt){e instanceof at&&(e.setMatrix(i),e.calculatePointInfo());var n=e.buildAnimation();t._animationArray.push(n)}})},t.prototype.buildLegendItems=function(){var t=[];this.dataContainer.getPipelines().forEach(function(e){if(e instanceof at){var i=e.getName();i&&((n=new Ct(i,e.getColor())).setId(e.getId()),t.push(n))}else if(e instanceof ut){var n,r=e.getName();r&&((n=new St(r,e.getColor())).setId(e.getId()),t.push(n))}}),this.legend.addLegends(t)},t.prototype.setSize=function(t,e,i){var n=parseInt(t),r=parseInt(e);this.width=n,this.height=r,this.container.style.width=n+"px",this.container.style.height=r+"px",D(this.topCanvas,n,r),D(this.middleCanvas,n,r),D(this.bottomCanvas,n,r),D(this.barCanvas,n,r),this.paddings=i||Object.assign({},Et),this.calculateLayout()},t.prototype.getSize=function(){return{width:this.width,height:this.height}},t.prototype.calculateLayout=function(){var t=this.width,e=this.height;this.legend.setWidth(this.width),this.buildLegendItems();var i=0;this.legend.isVisible()&&(i=this.legend.getHeight());var n=this.paddings.paddingLeft,r=0,s=this.optionManager.getDataZoom();s&&(r=this.barHeight);var o=e-this.paddings.paddingBottom-r,a=t-this.paddings.paddingLeft-this.paddings.paddingLeft,h=e-this.paddings.paddingTop-this.paddings.paddingBottom-r-i;if(s){var l=this.height-r,u=this.paddings.paddingLeft;this.progressBar.resize(u,l,a,r),this.progressBar.setBoundRect({x:0,y:l-3,width:t,height:r+6}),this.progressBar.setVisible(!0),this.progressBar.setLeftBtnValue(s.start),this.progressBar.setRightBtnValue(s.end);var c=this.optionManager.getXAxisCount()-1,p=Math.round(c*s.start),d=Math.round(c*s.end);this.optionManager.setIndexRange(p,d)}else this.progressBar.setVisible(!1);var f={x:n,y:o,width:a,height:h};this.coordinate=this.optionManager.createCoordinate(f),this._needRefresh=!0},t.prototype.destroy=function(){this._survivalTimer&&(clearTimeout(this._survivalTimer),this._survivalTimer=null),this._dettachEventListener(),this.destroyAllAnimation(),this._instanceId&&Rt[this._instanceId]&&delete Rt[this._instanceId],this.parentDom.contains(this.container)&&(this.parentDom.removeChild(this.container),this.parentDom.removeAttribute(Mt),this.topCanvas=null,this.middleCanvas=null,this.barCanvas=null,this.bottomCanvas=null,this.tipContainer=null),this.dataContainer.getPipelines().forEach(function(t){t&&"function"==typeof t.destroy&&t.destroy()})},t.prototype.createProgressBar=function(t){var e=t,i=this.height-e,n=this.paddings.paddingLeft,r=this.width-this.paddings.paddingLeft-this.paddings.paddingRight,s=new Q(this.topCanvas,this.optionManager,n,i,r,e,20,0,1,this._handleXAsixChange.bind(this),10,e);return s.setVisible(!1),s},t.prototype._handleXAsixChange=function(){var t=this,e=this.progressBar.getRightBtnValue(),i=this.progressBar.getLeftBtnValue(),n=this.optionManager.getXAxis(),r=1;n&&n.length>0&&(r=n.length-1);var s=Math.round(r*i),o=Math.round(r*e);this.optionManager.setIndexRange(s,o),this.coordinate.setStickCounts(o-s+1,this.coordinate.yStickCount);var a=this.optionManager.createXAxisLabels();this.coordinate.setXAxisLabels(a),this.coordinate.calculate(),this.coordinate._needRefresh=!0;var h=this.dataContainer.getPipelines(),l={x:this.coordinate.origin.x,y:this.coordinate.origin.y,width:this.coordinate.xAxisLength,height:this.coordinate.yAxisLength},u=this.optionManager.createMatrix(l);h.forEach(function(e){if(e instanceof at){var i=t.optionManager.getIndexRange();e.setMatrix(u),e.setIndexRange(i.startIndex,i.endIndex),e.needCalculate()&&(t._needRefresh=!0,e.calculatePointInfo())}})},t.prototype._legendSelect=function(t){var e=t.offsetX||t.layerX,i=t.offsetY||t.layerY;if(H(this.legend.getRectExtend(),e,i)){var n=this.legend.getLegendItemByPos(e,i);K(this.topCanvas,n?"pointer":"default")}},t.prototype._onPropertyChange=function(t){t&&this.propertyChangeHandler.handle(t)},t.prototype._legendClick=function(t){var e=t.offsetX||t.layerX,i=t.offsetY||t.layerY,n=this.legend.getLegendItemByPos(e,i);if(n){var r,s=n.getName();n.isDisable()?(r=this.dataContainer.getPipelineByName(s))&&r.setVisible(!0):(r=this.dataContainer.getPipelineByName(s))&&r.setVisible(!1),n.setDisable(!n.isDisable()),this.legend.refresh()}},t.prototype._leaveCanvas=function(){var t=this;this._hoverList.forEach(function(e){var i=t.dataContainer.getPipelineById(e);i&&i.onLeave&&i.onLeave()}),this._hoverList=[]},t.prototype._coordinateRegionListener=function(t){var e=this,i=this._getCoordinateRegion(),n=t.offsetX||t.layerX,r=t.offsetY||t.layerY;this.markPointAnimation&&(this.markPointAnimation.cancle(),this.markPointAnimation=null),this._clearTopCanvas();var s=this.dataContainer.getPipelines(),o={};H(i,n,r)&&this.optionManager.getFromOption("xAxis")&&(o=this.handleLineHoverData(n,r,s),this.markPointAnimation&&(this.markPointAnimation.cancle(),this.markPointAnimation=null),this.markPointAnimation=new st(100,function(t){var i=t/100;e._drawMarkPoints(n,r,o,4*i)}),this.markPointAnimation.run()),o.items=o.items||[];var a=[],h=[],l=this._hoverList.concat();s.forEach(function(t){if(t instanceof gt&&t.getHover(n,r,o)){var e=t.getId();e&&h.push(e)}});var u=this.legend.getLegendItemByPos(n,r);if(u){var c=u.getId(),p=this.dataContainer.getPipelineById(c);if(p instanceof ut&&p.getParent()){var d=p.getParent();d.setLabelParam(null);var f=d.getId();if(f&&h.indexOf(f)<0&&h.push(f),!u.isDisable()){h.push(c);var g=p.buildTooltipInfo();o.items.push(g),d.setLabelParam(g)}}}h.forEach(function(t){var e=l.indexOf(t);e<0?a.push(t):l.splice(e,1)}),l.forEach(function(t){var i=e.dataContainer.getPipelineById(t);i&&i.onLeave&&i.onLeave()}),a.forEach(function(t){var i=e.dataContainer.getPipelineById(t);i&&i.onEnter&&i.onEnter()}),this._hoverList=h,this._handleTooltip(n,r,o,this.optionManager,{width:this.topCanvas.width,height:this.topCanvas.height})},t.prototype._coordinateRegionWheelListener=function(t){var e=this._getCoordinateRegion();this.progressBar&&this.progressBar.isVisible()&&(e=function(t,e){if(null==t)return e;if(null==e)return t;var i=Math.min(t.x,e.x),n=Math.min(t.y,e.y);return{x:i,y:n,width:Math.max(t.x+t.width-i,e.x+e.width-i),height:Math.max(t.y+t.height-n,e.y+e.height-n)}}(e,this.progressBar.getRegion()));var i=t.offsetX||t.layerX,n=t.offsetY||t.layerY;if(t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault(),this.markPointAnimation&&(this.markPointAnimation.cancle(),this.markPointAnimation=null),H(e,i,n)){var r=t.wheelDelta,s=0;0!=(s=Math.abs(r)>10?r/120:r/3)&&this.eventHandler.dispatchPropertyChangeEvent({type:"mousewheel",source:this,value:s})}return!1},t.prototype._clearTopCanvas=function(){this.tooltip.hide(),this.markPointAnimation&&(this.markPointAnimation.cancle(),this.markPointAnimation=null);var t=this.topCanvas.getContext("2d"),e=parseInt(this.topCanvas.width),i=parseInt(this.topCanvas.height);t.save(),t.clearRect(0,0,e,i),t.restore()},t.prototype._handleTooltip=function(t,e,i,n,r){this.topCanvas&&this.tooltip.show(t,e,i,20,r,n)},t.prototype._getCoordinateRegion=function(){return this.coordinate.getRect()},t.prototype.refresh=function(){this._needRefresh=!0},t.prototype.handleLineHoverData=function(t,e,i){var n={},r=[];if(n.items=[],n.selectedPoints=r,n.items=[],i)for(var s=0;s<i.length;s++){var o=i[s];if(o&&o.isVisible()&&o instanceof at){var a=o.getPointInfo(t,e);null!=a&&a.point&&(n.items.push({seriesType:o.getSeriesType(),axisValueLabel:a.xValue,seriesName:o.getName(),chartLineId:o.getId(),value:a.yValue,color:o.getColor()}),r.push({seriesName:o.getName(),chartLineId:o.getId(),point:a.point,color:o.getColor()}),n.title||(n.title=a.xValue))}}return n},t.prototype.draw=function(){if(this.coordinate.needRefresh()&&null!=this.optionManager.getFromOption("xAxis")){var t=this.bottomCanvas.getContext("2d");t.save(),t.clearRect(0,0,this.width,this.height),t.restore(),t.save(),t.translate(.5,.5),this.coordinate.setXIndexInterval(this.optionManager.getIndexInterval()),this.coordinate.draw(t),t.restore(),this.coordinate._needRefresh=!1,this.legend._needRefresh=!0}this.legend&&this.legend.needRefresh()&&this.legend.draw(),this.progressBar.needRefresh()&&(this.progressBar.beforeDraw(this.barCanvas.getContext("2d")),this.progressBar.draw(this.barCanvas.getContext("2d"))),(this.dataContainer.dirtyList.length>0||this._needRefresh)&&(this._refreshPipeline(void 0),this.dataContainer.clearDirty()),this._needRefresh=!1},t.prototype._refreshPipeline=function(t){var e=this,i=this.dataContainer.pipelines,n=this.middleCanvas.getContext("2d");n.save(),n.clearRect(0,0,this.width,this.height),n.restore(),n.save(),i.forEach(function(i){i&&i.isVisible()&&(i instanceof at?(i.setIndexInterval(e.optionManager.getIndexInterval(),!0),t&&t<1?i.drawByPercent(n,t):i.draw(n)):i instanceof gt&&i.draw(n),e._needRefresh=!1,e.dataContainer.removeFromDirty(i))}),n.restore(),this._needRefresh=!1},t.prototype._drawMarkPoints=function(t,e,i,n){if(void 0===n&&(n=4),this._animationArray&&(this._animationArray.forEach(function(t){t instanceof st&&t.done(!0)}),this._animationArray=null),this.topCanvas&&null!=this.optionManager.getFromOption("xAxis")){var r=this.topCanvas.getContext("2d"),s=t-this.coordinate.origin.x,o=(0==this.coordinate.xSpan?0:Math.round(s/this.coordinate.xSpan))*this.coordinate.xSpan+this.coordinate.origin.x,a={x:o,y:this.coordinate.origin.y},h={x:o,y:this.coordinate.origin.y-this.coordinate.yAxisLength};r.save(),L(r,a,h,"#c0c0c0",1);for(var l=i.selectedPoints,u=0;u<l.length;u++){var c=l[u];if(c&&c.point){var p=c.chartLineId,d=this.dataContainer.getPipelineById(p);if(d&&!d.isVisible())continue;I(r,c.point.x,c.point.y,n,c.color,"#FFF")}}r.restore(),A(r,[{x:this.coordinate.origin.x,y:e},{x:this.coordinate.origin.x+this.coordinate.xAxisLength,y:e}],"#c0c0c0",1,[2,1]),r.restore()}},t.prototype.needRefresh=function(){return this._needRefresh||this.coordinate&&this.coordinate.needRefresh()||this.progressBar&&this.progressBar.needRefresh()||this.legend&&this.legend.needRefresh()||this.dataContainer.isDirty()},t.prototype.survivalMonitor=function(){var t=this;this._survivalTimer&&(clearTimeout(this._survivalTimer),this._survivalTimer=null),document.body.contains(this.container)&&document.body.contains(this.middleCanvas)?this._survivalTimer=setTimeout(function(){t.survivalMonitor()},2e3):this.destroy()},t.prototype.destroyAllAnimation=function(){this.markPointAnimation&&(this.markPointAnimation.cancle(),this.markPointAnimation=null)},t.prototype.getProperty=function(t){return this.properties[t]},t.prototype.setProperty=function(t,e){null!=t&&(this.properties[t]=e)},t}();function Bt(){var t=document.createElement("canvas");return t.style.position="absolute",t}window.UWChart=kt}])},function(t,e,i){"use strict";
/** @license React v16.4.2
 * react.production.min.js
 *
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=i(4),r=i(5),s=i(6),o=i(7),a="function"==typeof Symbol&&Symbol.for,h=a?Symbol.for("react.element"):60103,l=a?Symbol.for("react.portal"):60106,u=a?Symbol.for("react.fragment"):60107,c=a?Symbol.for("react.strict_mode"):60108,p=a?Symbol.for("react.profiler"):60114,d=a?Symbol.for("react.provider"):60109,f=a?Symbol.for("react.context"):60110,g=a?Symbol.for("react.async_mode"):60111,y=a?Symbol.for("react.forward_ref"):60112;a&&Symbol.for("react.timeout");var v="function"==typeof Symbol&&Symbol.iterator;function x(t){for(var e=arguments.length-1,i="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=0;n<e;n++)i+="&args[]="+encodeURIComponent(arguments[n+1]);r(!1,"Minified React error #"+t+"; visit %s for the full message or use the non-minified dev environment for full errors and additional helpful warnings. ",i)}var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}};function _(t,e,i){this.props=t,this.context=e,this.refs=s,this.updater=i||m}function b(){}function w(t,e,i){this.props=t,this.context=e,this.refs=s,this.updater=i||m}_.prototype.isReactComponent={},_.prototype.setState=function(t,e){"object"!=typeof t&&"function"!=typeof t&&null!=t&&x("85"),this.updater.enqueueSetState(this,t,e,"setState")},_.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")},b.prototype=_.prototype;var L=w.prototype=new b;L.constructor=w,n(L,_.prototype),L.isPureReactComponent=!0;var A={current:null},P=Object.prototype.hasOwnProperty,C={key:!0,ref:!0,__self:!0,__source:!0};function I(t,e,i){var n=void 0,r={},s=null,o=null;if(null!=e)for(n in void 0!==e.ref&&(o=e.ref),void 0!==e.key&&(s=""+e.key),e)P.call(e,n)&&!C.hasOwnProperty(n)&&(r[n]=e[n]);var a=arguments.length-2;if(1===a)r.children=i;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}if(t&&t.defaultProps)for(n in a=t.defaultProps)void 0===r[n]&&(r[n]=a[n]);return{$$typeof:h,type:t,key:s,ref:o,props:r,_owner:A.current}}function S(t){return"object"==typeof t&&null!==t&&t.$$typeof===h}var R=/\/+/g,M=[];function E(t,e,i,n){if(M.length){var r=M.pop();return r.result=t,r.keyPrefix=e,r.func=i,r.context=n,r.count=0,r}return{result:t,keyPrefix:e,func:i,context:n,count:0}}function k(t){t.result=null,t.keyPrefix=null,t.func=null,t.context=null,t.count=0,10>M.length&&M.push(t)}function B(t,e,i,n){var r=typeof t;"undefined"!==r&&"boolean"!==r||(t=null);var s=!1;if(null===t)s=!0;else switch(r){case"string":case"number":s=!0;break;case"object":switch(t.$$typeof){case h:case l:s=!0}}if(s)return i(n,t,""===e?"."+V(t,0):e),1;if(s=0,e=""===e?".":e+":",Array.isArray(t))for(var o=0;o<t.length;o++){var a=e+V(r=t[o],o);s+=B(r,a,i,n)}else if(null===t||void 0===t?a=null:a="function"==typeof(a=v&&t[v]||t["@@iterator"])?a:null,"function"==typeof a)for(t=a.call(t),o=0;!(r=t.next()).done;)s+=B(r=r.value,a=e+V(r,o++),i,n);else"object"===r&&x("31","[object Object]"===(i=""+t)?"object with keys {"+Object.keys(t).join(", ")+"}":i,"");return s}function V(t,e){return"object"==typeof t&&null!==t&&null!=t.key?function(t){var e={"=":"=0",":":"=2"};return"$"+(""+t).replace(/[=:]/g,function(t){return e[t]})}(t.key):e.toString(36)}function O(t,e){t.func.call(t.context,e,t.count++)}function H(t,e,i){var n=t.result,r=t.keyPrefix;t=t.func.call(t.context,e,t.count++),Array.isArray(t)?T(t,n,i,o.thatReturnsArgument):null!=t&&(S(t)&&(e=r+(!t.key||e&&e.key===t.key?"":(""+t.key).replace(R,"$&/")+"/")+i,t={$$typeof:h,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}),n.push(t))}function T(t,e,i,n,r){var s="";null!=i&&(s=(""+i).replace(R,"$&/")+"/"),e=E(e,s,n,r),null==t||B(t,"",H,e),k(e)}var N={Children:{map:function(t,e,i){if(null==t)return t;var n=[];return T(t,n,null,e,i),n},forEach:function(t,e,i){if(null==t)return t;e=E(null,null,e,i),null==t||B(t,"",O,e),k(e)},count:function(t){return null==t?0:B(t,"",o.thatReturnsNull,null)},toArray:function(t){var e=[];return T(t,e,null,o.thatReturnsArgument),e},only:function(t){return S(t)||x("143"),t}},createRef:function(){return{current:null}},Component:_,PureComponent:w,createContext:function(t,e){return void 0===e&&(e=null),(t={$$typeof:f,_calculateChangedBits:e,_defaultValue:t,_currentValue:t,_currentValue2:t,_changedBits:0,_changedBits2:0,Provider:null,Consumer:null}).Provider={$$typeof:d,_context:t},t.Consumer=t},forwardRef:function(t){return{$$typeof:y,render:t}},Fragment:u,StrictMode:c,unstable_AsyncMode:g,unstable_Profiler:p,createElement:I,cloneElement:function(t,e,i){(null===t||void 0===t)&&x("267",t);var r=void 0,s=n({},t.props),o=t.key,a=t.ref,l=t._owner;if(null!=e){void 0!==e.ref&&(a=e.ref,l=A.current),void 0!==e.key&&(o=""+e.key);var u=void 0;for(r in t.type&&t.type.defaultProps&&(u=t.type.defaultProps),e)P.call(e,r)&&!C.hasOwnProperty(r)&&(s[r]=void 0===e[r]&&void 0!==u?u[r]:e[r])}if(1===(r=arguments.length-2))s.children=i;else if(1<r){u=Array(r);for(var c=0;c<r;c++)u[c]=arguments[c+2];s.children=u}return{$$typeof:h,type:t.type,key:o,ref:a,props:s,_owner:l}},createFactory:function(t){var e=I.bind(null,t);return e.type=t,e},isValidElement:S,version:"16.4.2",__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:{ReactCurrentOwner:A,assign:n}},j={default:N},D=j&&N||j;t.exports=D.default?D.default:D},function(t,e,i){"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var n=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable;t.exports=function(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},i=0;i<10;i++)e["_"+String.fromCharCode(i)]=i;if("**********"!==Object.getOwnPropertyNames(e).map(function(t){return e[t]}).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach(function(t){n[t]=t}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(t){return!1}}()?Object.assign:function(t,e){for(var i,o,a=function(t){if(null===t||void 0===t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}(t),h=1;h<arguments.length;h++){for(var l in i=Object(arguments[h]))r.call(i,l)&&(a[l]=i[l]);if(n){o=n(i);for(var u=0;u<o.length;u++)s.call(i,o[u])&&(a[o[u]]=i[o[u]])}}return a}},function(t,e,i){"use strict";var n=function(t){};t.exports=function(t,e,i,r,s,o,a,h){if(n(e),!t){var l;if(void 0===e)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[i,r,s,o,a,h],c=0;(l=new Error(e.replace(/%s/g,function(){return u[c++]}))).name="Invariant Violation"}throw l.framesToPop=1,l}}},function(t,e,i){"use strict";t.exports={}},function(t,e,i){"use strict";function n(t){return function(){return t}}var r=function(){};r.thatReturns=n,r.thatReturnsFalse=n(!1),r.thatReturnsTrue=n(!0),r.thatReturnsNull=n(null),r.thatReturnsThis=function(){return this},r.thatReturnsArgument=function(t){return t},t.exports=r},function(t,e,i){"use strict";i.r(e);var n=i(0),r=i.n(n);i(2);var s=window.UWChart,o=i(1),a=i.n(o),h=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return h(e,t),e.prototype.componentDidMount=function(){var t=this.props,e=t.title,i=t.grid,n=t.legend,r=t.xAxis,o=t.yAxis,a=t.dataZoom,h=t.tooltip,l=t.color,u=t.series;this.chart=new s("line",this.dom);var c={title:e,legend:n,xAxis:r,yAxis:o,dataZoom:a,tooltip:h,color:l,series:u,grid:i};this.chart.setOption(c)},e.prototype.componentWillReceiveProps=function(t){a()(this.props,t)||this.chart.setOption(t||{})},e.prototype.render=function(){var t=this,e=this.props,i=e.height,n=e.width;return r.a.createElement("div",{style:{height:i,width:n}},r.a.createElement("div",{style:{height:"100%",width:"100%"},ref:function(e){return t.dom=e}}))},e.__DISPLAY_NAME__="line",e}(r.a.Component);i(2);var u=window.UWChart,c=function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return c(e,t),e.prototype.componentDidMount=function(){var t=this.props,e=t.title,i=t.grid,n=t.legend,r=t.tooltip,s=t.color,o=t.series;this.chart=new u("pie",this.dom);var a={title:e,legend:n,tooltip:r,color:s,series:o,grid:i};this.chart.setOption(a)},e.prototype.componentWillReceiveProps=function(t){a()(this.props,t)||this.chart.setOption(t||{})},e.prototype.render=function(){var t=this,e=this.props,i=e.height,n=e.width;return r.a.createElement("div",{style:{height:i,width:n}},r.a.createElement("div",{style:{height:"100%",width:"100%"},ref:function(e){return t.dom=e}}))},e.__DISPLAY_NAME__="pie",e}(r.a.Component);i.d(e,"Line",function(){return l}),i.d(e,"Pie",function(){return p})}])});
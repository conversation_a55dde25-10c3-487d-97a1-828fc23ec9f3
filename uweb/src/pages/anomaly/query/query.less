/*
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

.metis-anomaly-chart {
  .tc-15-page {
    border-top: none;
  }
}
.metis-chart-block {
  min-width: 315px;
  margin-bottom: 10px;
  .tc-panel {
    height: 405px;
  }
  .tc-panel .tc-panel-hd {
    margin-bottom: 5px;
  }

  .metis-chart-title {
    display: flex;

    .metis-title-text {
      width: 90%;
      .metis-text-name {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    } 

    .metis-title-icon {
      width: 10%;
      a {
        float: right;
      }
      .icon-expand {
        vertical-align: middle;
        width: 11px;
        height: 11px;
      }
    }
  }

  .metis-chart {
    margin-top: 10px;
  }

  .metis-chart-markarea {
    margin-top: 5px;

    .metis-markarea-text {
      text-align: center;
      width: 90px;
      display: inline-block;
      position: relative;
      left: 38%;
      margin-top: 7px;
      font-weight: bold;
    }
  }
}

.tc-g {
  margin-right: -2px;
}
.tc-g .uw-chart-block {
  padding-right: 2px;
}
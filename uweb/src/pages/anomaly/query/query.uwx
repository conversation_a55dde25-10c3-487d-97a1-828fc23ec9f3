/*
  Tencent is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

{/* import src="./query.model.uw" */}
{/* import src="./query.ctrl.uw" */}
{/* style src="./query.less" */}

<Page title="异常查询">
  <Content>
    <ActionBar>
      <ActionBar.ActionArea>
        <DateTimePicker value={$model.query.data.dateTime} onChange={$controller.query.onSearchChange('dateTime')} format="YYYY-MM-DD HH:mm" />
        <Input placeholder="指标集 / 指标集ID" style={{ width: 200 }} addonBefore="指标集" value={$model.query.data.viewName}
          onChange={$controller.query.onSearchChange('viewName')} onPressEnter={$controller.query.onSearch} />
        <Input placeholder="指标 / 指标ID" style={{ width: 200 }} addonBefore="指标" value={$model.query.data.attrName}
          onChange={$controller.query.onSearchChange('attrName')} onPressEnter={$controller.query.onSearch} />
        <Button type="primary" onClick={$controller.query.onSearch} style={{ marginLeft: 10 }}>搜索</Button>
      </ActionBar.ActionArea>
    </ActionBar>
    {
      $model.query.data.loading ? <Plugins.PageLoading /> : $model.query.data.anomalyList.length === 0 ? (
        <Plugins.Placeholder
          image={require('../../assets/no-server.svg')}
          title={$model.query.data.errMsg ? '异常查询接口异常' : '当前查询条件下没有异常'}
          content={$model.query.data.errMsg}
        />
      ) : (
          <div className="metis-anomaly-chart">
            <Row>
              {
                $model.query.data.anomalyList.map((item) => {
                  const titleName = `[${item.viewId}]${item.viewName} - [${item.attrId}]${item.attrName}`;
                  const title = (
                    <div title={titleName} className="metis-title-text">
                      <p className="metis-text-name">{titleName}</p>
                    </div>
                  );
                  return (
                    <Col col={8} key={item.id} className="metis-chart-block">
                      <Panel
                        title={
                          <div className="metis-chart-title">
                            {title}
                            <div className="metis-title-icon">
                              <a title="展开大图" onClick={() => $controller.query.onChartExtend(title, item.chartOption)}><Icon type="expand" /></a>
                            </div>
                          </div>
                        }
                      >
                        <div className="metis-chart"><Plugins.Chart option={item.chartOption} /></div>
                        <div className="metis-chart-markarea">
                          {$controller.query.genMarkArea(item.markFlag, item.id)}
                        </div>
                      </Panel>
                    </Col>
                  );
                })
              }
            </Row>
            <Pagination
              total={$model.query.data.total}
              currentPage={$model.query.data.currentPage}
              pageSize={$model.query.data.pageSize}
              pageSizeOptions={$model.query.data.pageSizeOptions}
              onChange={$controller.query.onPaginationChange}
            />
          </div>
        )
    }
    <Dialog
      title={$model.query.data.extendChart.title}
      visible={$model.query.data.extendChart.visible}
      style={{ width: 1000 }}
      onCancel={$controller.query.onChartCancel}
      cancelText="关闭"
    >
      {$model.query.data.extendChart.visible && <Plugins.Chart option={$model.query.data.extendChart.option} height={400} />}
    </Dialog>
  </Content>
</Page>
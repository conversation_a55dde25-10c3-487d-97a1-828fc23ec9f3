/*
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

const moment = require('moment');
const { getDefaultChartOption } = require('../../common.js');

Controller('query', {
  onLoad() {
    $model.query.setData({
      defaultChartOption: getDefaultChartOption(),
    });
    $model.query.queryAnomalyList();
  },

  onSearchChange(field) {
    return (value) => {
      $model.query.data[field] = value;
      if (field === 'dateTime') {
        $model.query.queryAnomalyList();
      }
    };
  },

  onSearch() {
    $model.query.setData({ currentPage: 1 });
    $model.query.queryAnomalyList();
  },

  onPaginationChange(currentPage, pageSize) {
    $model.query.setData({
      currentPage: $model.query.data.currentPage !== currentPage ? currentPage : 1,
      pageSize: pageSize,
    });
    $model.query.queryAnomalyList();
  },

  onChartExtend(title, option) {
    $model.query.setData({ extendChart: { visible: true, title, option } });
  },

  onChartCancel() {
    $model.query.setData({ extendChart: { visible: false, title: '', option: {} } });
  },

  genMarkArea(markFlag, id) {
    const { markFlagList = [] } = $model.query.data;
    const markFlagInfo = _.filter(markFlagList, (item) => item.id === id);
    let disabled = false;
    let positiveLoading = false;
    let negativeLoading = false;
    let cancelLoading = false;
    if (markFlagInfo.length > 0) {
      disabled = true;
      switch (markFlagInfo[0].newMarkFlag) {
        case 0:
          cancelLoading = true;
          break;
        case 1:
          positiveLoading = true;
          break;
        case 2:
          negativeLoading = true;
          break;
        default:
      }
    }
    const loadingIcon = <span><Icon type="loading" /> </span>;
    switch (markFlag) {
      case 0:
        return (
          <div>
            <Button style={{ float: 'left' }} disabled={disabled} onClick={() => $model.query.updateMarkFlag(1, id)}>
              {positiveLoading && loadingIcon}标记为正样本
            </Button>
            <Button style={{ float: 'right' }} disabled={disabled} onClick={() => $model.query.updateMarkFlag(2, id)}>
              {negativeLoading && loadingIcon}标记为负样本
            </Button>
          </div>
        );
        break;
      case 1:
        return (
          <div>
            <span className="uw-markarea-text">已标记正样本</span>
            <Button style={{ float: 'right' }} disabled={disabled} onClick={() => $model.query.updateMarkFlag(0, id)}>
              {cancelLoading && loadingIcon}取消标记
            </Button>
          </div>
        );
        break;
      case 2:
        return (
          <div>
            <span className="uw-markarea-text">已标记负样本</span>
            <Button style={{ float: 'right' }} disabled={disabled} onClick={() => $model.query.updateMarkFlag(0, id)}>
              {cancelLoading && loadingIcon}取消标记
            </Button>
          </div>
        );
        break;
      default:
        return undefined;
    }
  }
})
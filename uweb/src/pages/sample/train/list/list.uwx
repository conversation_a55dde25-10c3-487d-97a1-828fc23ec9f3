/*
  Tencent is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

{/* import src="./list.model.uw" */}
{/* import src="./list.ctrl.uw" */}

<Page title="训练模型">
  <Content loading={$model.list.data.pageLoading}>
    {
      $model.list.data.isEmpty ? (
        <Plugins.Placeholder
          image={require('../../../assets/no-server.svg')}
          title="训练任务列表为空"
          content={<Button type="primary" onClick={() => { $props.history.push('/sample/train/create') }}>新建训练</Button>}
        />
      ) : (
          <div>
            <ActionBar>
              <ActionBar.ActionArea>
                <Button type="primary" onClick={() => { $props.history.push('/sample/train/create') }}>新建训练</Button>
              </ActionBar.ActionArea>
              <ActionBar.ExtraArea>
                <div className="filter-text">
                  <a onClick={$controller.list.onAdvanceFilterExpand}>高级搜索<Icon type="arrow-down" /></a>
                </div>
                <SearchBox
                  placeholder="任务ID / 模型名称"
                  value={$model.list.data.searchText}
                  onChange={$controller.list.onSearchChange}
                  onSearch={$controller.list.onSearch}
                />
              </ActionBar.ExtraArea>
            </ActionBar>
            <Panel>
              <Plugins.Filter
                expand={$model.list.data.expand}
                filterContent={[
                  {
                    label: '样本来源',
                    component:
                      <Dropdown
                        placeholder="请选择"
                        options={$model.list.data.sourceList}
                        onChange={$controller.list.onAdvanceFilterChange('source')}
                        value={$model.list.data.advanceSearchTemp.source.value}
                      />
                  },
                  {
                    label: '任务状态',
                    component:
                      <Dropdown
                        placeholder="请选择"
                        valueWithLabel={true}
                        options={$model.list.data.taskStatusList}
                        onChange={$controller.list.onAdvanceFilterChange('taskStatus')}
                        value={$model.list.data.advanceSearchTemp.taskStatus.value}
                      />
                  },
                  {
                    label: '开始时间',
                    component:
                      <DateTimePicker
                        value={$model.list.data.advanceSearchTemp.beginDateTime.value}
                        onChange={$controller.list.onAdvanceFilterChange('beginDateTime')}
                        format="YYYY-MM-DD HH:mm"
                      />
                  },
                ]}
                onQuery={$controller.list.onAdvanceFilterQuery}
                filterResult={$controller.list.getAdvanceFilterResult()}
                onResultClose={(item) => { $controller.list.onAdvanceFilterResultClose(item.key); }}
                onResultClear={$controller.list.onAdvanceFilterClear}
              />
              <Table
                rowKey="id"
                columns={$controller.list.getTableColumns()}
                loading={$model.list.data.tableLoading}
                dataSource={$model.list.data.dataSource}
                pagination={{
                  total: $model.list.data.total,
                  pageSize: $model.list.data.pageSize,
                  currentPage: $model.list.data.currentPage,
                  pageSizeOptions: $model.list.data.pageSizeOptions,
                }}
                onChange={$controller.list.onTableChange}
                placeholder={
                  $model.list.data.errMsg ?
                    <Plugins.ErrorMessage>{$model.list.data.errMsg}</Plugins.ErrorMessage> :
                    $model.list.data.dataSource.length === 0 ? (
                      <span>
                        列表为空, <a className="uw-table-reset-search" onClick={$controller.list.onSearchReset}>清空查询条件</a>
                      </span>
                    ) : ''
                }
              />
            </Panel>
          </div>
        )
    }
  </Content>
</Page>
/*
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

Model('sampleInfoUpdate', {
  data: {
    updatePositiveOrNegative: '',
    updateSampleName: '',
    updateSampleVisible: false,
    updateSource: '',
    updateTrainOrTest: '',
    updateErrorMsg: null,
    updateKeys: [],
    readyToUpdate: true,
    singleUpdate: false,
    oversizeText: '',
  },

  setData(info) {
    Object.assign(this.data, info);
  },

  // 编辑样本请求
  updateSample() {
    uw.request({
      url: '/UpdateSample',
      method: 'POST',
      data: {
        idList: this.data.updateKeys,
        data: {
          attrId: '',
          attrName: '',
          viewId: '',
          viewName: '',
          updateTime: '',
          time: '',
          source: this.data.updateSource,
          trainOrTest: this.data.updateTrainOrTest,
          positiveOrNegative: this.data.updatePositiveOrNegative,
          window: '',
          dataTime: '',
          typeAbnormal: '',
          typeStable: '',
          typeShape: '',
        },
      },
      success: ({ code, data, msg }) => {
        if (code === 0) {
          Notification.succeed({ title: '修改成功' });
          this.setData({ updateSampleVisible: false });
          $model.sampleInfo.resetSelected();
          $model.sampleInfo.queryTrainSource();
          $model.sampleInfo.querySample();
          this.setData({ updateErrorMsg: null });
        }
        else {
          this.setData({ updateErrorMsg: msg });
        }
      },
    });
  },
});
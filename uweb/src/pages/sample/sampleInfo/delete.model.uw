/*
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

const CONFIG = require('../../constant.json');

Model('sampleInfoDelete', {
  data: {},

  setData(info) {
    Object.assign(this.data, info);
  },

  // 删除样本请求
  deleteSample(record) {
    uw.request({
      url: '/DeleteSample',
      method: 'POST',
      data: { id: [record.id] },
      success: ({ code, data, msg }) => {
        if (code === 0) {
          Notification.succeed({ title: '删除成功' });
          $model.sampleInfo.resetSelected();
          $model.sampleInfo.querySample();
        }
        else {
          Notification.error({
            title: '删除失败',
            content: msg,
            delay: CONFIG.errorDelay,
          });
        }
      },
    });
  },
});
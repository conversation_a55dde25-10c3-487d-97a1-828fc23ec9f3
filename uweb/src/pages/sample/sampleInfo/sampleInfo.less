/*
  <PERSON><PERSON> is pleased to support the open source community by making Metis available.
  Copyright (C) 2018 THL A29 Limited, a Tencent company. All rights reserved.
  Licensed under the BSD 3-Clause License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
  https://opensource.org/licenses/BSD-3-Clause
  Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

.filter-text {
  height: 30px;
  cursor: pointer;
  line-height: 30px;
  float: left;
  margin-right : 5px;
  a {
    text-decoration: none;
  }
}

.option-text {
  margin-left: 5px
}

.import-input {
  width: 180px
}

.uw-table-reset-search {
  vertical-align: baseline !important;
}

.filter-form .form-label {
  width: 85px;
}

.uw-upload-info {
  .uw-upload-text {
    margin-top: 20px;
    margin-bottom: 10px;
  }
  .form-input, .form-label, .form-output {
    padding-bottom: 0;
  }
  .param-box {
    padding-left: 20px;
  }
}